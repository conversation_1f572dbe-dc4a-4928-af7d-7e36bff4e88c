{"ast": null, "code": "import { get, post, put, del } from '../api/apiClient';\nconst BASE_URL = '/api/job-category';\nexport const jobCategoryService = {\n  // Get all job categories\n  getAllJobCategories: async () => {\n    return get(BASE_URL);\n  },\n  // Get job category by ID\n  getJobCategoryById: async id => {\n    return get(`${BASE_URL}/${id}`);\n  },\n  // Create a new job category\n  createJobCategory: async jobCategory => {\n    return post(BASE_URL, jobCategory);\n  },\n  // Update an existing job category\n  updateJobCategory: async jobCategory => {\n    return put(BASE_URL, jobCategory);\n  },\n  // Delete a job category\n  deleteJobCategory: async id => {\n    return del(`${BASE_URL}/${id}`);\n  }\n};", "map": {"version": 3, "names": ["get", "post", "put", "del", "BASE_URL", "jobCategoryService", "getAllJobCategories", "getJobCategoryById", "id", "createJobCategory", "jobCategory", "updateJobCategory", "deleteJobCategory"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/services/job/jobCategoryService.ts"], "sourcesContent": ["import { JobCategory } from '../../models';\nimport { get, post, put, del } from '../api/apiClient';\n\nconst BASE_URL = '/api/job-category';\n\nexport const jobCategoryService = {\n  // Get all job categories\n  getAllJobCategories: async (): Promise<JobCategory[]> => {\n    return get<JobCategory[]>(BASE_URL);\n  },\n\n  // Get job category by ID\n  getJobCategoryById: async (id: number): Promise<JobCategory> => {\n    return get<JobCategory>(`${BASE_URL}/${id}`);\n  },\n\n  // Create a new job category\n  createJobCategory: async (jobCategory: JobCategory): Promise<JobCategory> => {\n    return post<JobCategory>(BASE_URL, jobCategory);\n  },\n\n  // Update an existing job category\n  updateJobCategory: async (jobCategory: JobCategory): Promise<JobCategory> => {\n    return put<JobCategory>(BASE_URL, jobCategory);\n  },\n\n  // Delete a job category\n  deleteJobCategory: async (id: number): Promise<void> => {\n    return del<void>(`${BASE_URL}/${id}`);\n  }\n};\n"], "mappings": "AACA,SAASA,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,QAAQ,kBAAkB;AAEtD,MAAMC,QAAQ,GAAG,mBAAmB;AAEpC,OAAO,MAAMC,kBAAkB,GAAG;EAChC;EACAC,mBAAmB,EAAE,MAAAA,CAAA,KAAoC;IACvD,OAAON,GAAG,CAAgBI,QAAQ,CAAC;EACrC,CAAC;EAED;EACAG,kBAAkB,EAAE,MAAOC,EAAU,IAA2B;IAC9D,OAAOR,GAAG,CAAc,GAAGI,QAAQ,IAAII,EAAE,EAAE,CAAC;EAC9C,CAAC;EAED;EACAC,iBAAiB,EAAE,MAAOC,WAAwB,IAA2B;IAC3E,OAAOT,IAAI,CAAcG,QAAQ,EAAEM,WAAW,CAAC;EACjD,CAAC;EAED;EACAC,iBAAiB,EAAE,MAAOD,WAAwB,IAA2B;IAC3E,OAAOR,GAAG,CAAcE,QAAQ,EAAEM,WAAW,CAAC;EAChD,CAAC;EAED;EACAE,iBAAiB,EAAE,MAAOJ,EAAU,IAAoB;IACtD,OAAOL,GAAG,CAAO,GAAGC,QAAQ,IAAII,EAAE,EAAE,CAAC;EACvC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}