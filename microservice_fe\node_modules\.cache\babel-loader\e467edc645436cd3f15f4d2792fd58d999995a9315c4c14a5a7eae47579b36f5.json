{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\components\\\\common\\\\DatePickerField.tsx\";\nimport React from 'react';\nimport { DatePicker } from '@mui/x-date-pickers/DatePicker';\nimport { formatDateForInput } from '../../utils/dateUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n/**\n * Tùy chỉnh DatePicker hiển thị ngày tháng theo định dạng Việt Nam (DD/MM/YYYY)\n */\nfunction DatePickerField({\n  label,\n  value,\n  onChange,\n  name,\n  required = false,\n  disabled = false,\n  fullWidth = true,\n  error = false,\n  helperText,\n  sx = {},\n  minDate,\n  maxDate\n}) {\n  // Chuyển đổi giá trị thành đối tượng Date\n  const dateValue = value ? new Date(value) : null;\n\n  // <PERSON><PERSON> lý sự kiện thay đổi ngày\n  const handleDateChange = date => {\n    if (date && !isNaN(date.getTime())) {\n      // Chuyển đổi thành chuỗi YYYY-MM-DD để lưu trữ\n      const formattedDate = formatDateForInput(date);\n      onChange(formattedDate);\n    } else {\n      onChange('');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(DatePicker, {\n    label: label,\n    value: dateValue,\n    onChange: handleDateChange,\n    format: \"dd/MM/yyyy\",\n    minDate: minDate,\n    maxDate: maxDate,\n    slotProps: {\n      textField: {\n        name,\n        required,\n        disabled,\n        fullWidth,\n        error,\n        helperText,\n        sx: {\n          '& .MuiInputLabel-root': {\n            transform: 'translate(14px, -9px) scale(0.75)',\n            background: '#fff',\n            padding: '0 8px'\n          },\n          '& .MuiInputBase-input': {\n            fontFamily: 'inherit',\n            fontSize: '1rem'\n          },\n          ...sx\n        }\n      }\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n}\n_c = DatePickerField;\n;\nexport default DatePickerField;\nvar _c;\n$RefreshReg$(_c, \"DatePickerField\");", "map": {"version": 3, "names": ["React", "DatePicker", "formatDateForInput", "jsxDEV", "_jsxDEV", "DatePickerField", "label", "value", "onChange", "name", "required", "disabled", "fullWidth", "error", "helperText", "sx", "minDate", "maxDate", "dateValue", "Date", "handleDateChange", "date", "isNaN", "getTime", "formattedDate", "format", "slotProps", "textField", "transform", "background", "padding", "fontFamily", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/common/DatePickerField.tsx"], "sourcesContent": ["import React from 'react';\nimport { DatePicker } from '@mui/x-date-pickers/DatePicker';\nimport { TextField, TextFieldProps } from '@mui/material';\nimport { formatDateForInput } from '../../utils/dateUtils';\n\ninterface DatePickerFieldProps {\n  label: string;\n  value: string | Date | undefined;\n  onChange: (date: string) => void;\n  name?: string;\n  required?: boolean;\n  disabled?: boolean;\n  fullWidth?: boolean;\n  error?: boolean;\n  helperText?: string;\n  sx?: any;\n  minDate?: Date;\n  maxDate?: Date;\n}\n\n/**\n * Tùy chỉnh DatePicker hiển thị ngày tháng theo định dạng Việt Nam (DD/MM/YYYY)\n */\nfunction DatePickerField({\n  label,\n  value,\n  onChange,\n  name,\n  required = false,\n  disabled = false,\n  fullWidth = true,\n  error = false,\n  helperText,\n  sx = {},\n  minDate,\n  maxDate,\n}: DatePickerFieldProps) {\n  // Chuyển đổi giá trị thành đối tượng Date\n  const dateValue = value ? new Date(value) : null;\n\n  // Xử lý sự kiện thay đổi ngày\n  const handleDateChange = (date: Date | null) => {\n    if (date && !isNaN(date.getTime())) {\n      // Chuyển đổi thành chuỗi YYYY-MM-DD để lưu trữ\n      const formattedDate = formatDateForInput(date);\n      onChange(formattedDate);\n    } else {\n      onChange('');\n    }\n  };\n\n  return (\n    <DatePicker\n      label={label}\n      value={dateValue}\n      onChange={handleDateChange}\n      format=\"dd/MM/yyyy\"\n      minDate={minDate}\n      maxDate={maxDate}\n      slotProps={{\n        textField: {\n          name,\n          required,\n          disabled,\n          fullWidth,\n          error,\n          helperText,\n          sx: {\n            '& .MuiInputLabel-root': {\n              transform: 'translate(14px, -9px) scale(0.75)',\n              background: '#fff',\n              padding: '0 8px'\n            },\n            '& .MuiInputBase-input': {\n              fontFamily: 'inherit',\n              fontSize: '1rem'\n            },\n            ...sx\n          } as TextFieldProps['sx'],\n        }\n      }}\n    />\n  );\n};\n\nexport default DatePickerField;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,gCAAgC;AAE3D,SAASC,kBAAkB,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAiB3D;AACA;AACA;AACA,SAASC,eAAeA,CAAC;EACvBC,KAAK;EACLC,KAAK;EACLC,QAAQ;EACRC,IAAI;EACJC,QAAQ,GAAG,KAAK;EAChBC,QAAQ,GAAG,KAAK;EAChBC,SAAS,GAAG,IAAI;EAChBC,KAAK,GAAG,KAAK;EACbC,UAAU;EACVC,EAAE,GAAG,CAAC,CAAC;EACPC,OAAO;EACPC;AACoB,CAAC,EAAE;EACvB;EACA,MAAMC,SAAS,GAAGX,KAAK,GAAG,IAAIY,IAAI,CAACZ,KAAK,CAAC,GAAG,IAAI;;EAEhD;EACA,MAAMa,gBAAgB,GAAIC,IAAiB,IAAK;IAC9C,IAAIA,IAAI,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE;MAClC;MACA,MAAMC,aAAa,GAAGtB,kBAAkB,CAACmB,IAAI,CAAC;MAC9Cb,QAAQ,CAACgB,aAAa,CAAC;IACzB,CAAC,MAAM;MACLhB,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC;EAED,oBACEJ,OAAA,CAACH,UAAU;IACTK,KAAK,EAAEA,KAAM;IACbC,KAAK,EAAEW,SAAU;IACjBV,QAAQ,EAAEY,gBAAiB;IAC3BK,MAAM,EAAC,YAAY;IACnBT,OAAO,EAAEA,OAAQ;IACjBC,OAAO,EAAEA,OAAQ;IACjBS,SAAS,EAAE;MACTC,SAAS,EAAE;QACTlB,IAAI;QACJC,QAAQ;QACRC,QAAQ;QACRC,SAAS;QACTC,KAAK;QACLC,UAAU;QACVC,EAAE,EAAE;UACF,uBAAuB,EAAE;YACvBa,SAAS,EAAE,mCAAmC;YAC9CC,UAAU,EAAE,MAAM;YAClBC,OAAO,EAAE;UACX,CAAC;UACD,uBAAuB,EAAE;YACvBC,UAAU,EAAE,SAAS;YACrBC,QAAQ,EAAE;UACZ,CAAC;UACD,GAAGjB;QACL;MACF;IACF;EAAE;IAAAkB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEN;AAACC,EAAA,GA5DQhC,eAAe;AA4DvB;AAED,eAAeA,eAAe;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}