{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\components\\\\common\\\\ErrorAlert.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Alert, AlertTitle, Box, IconButton, Collapse } from '@mui/material';\nimport CloseIcon from '@mui/icons-material/Close';\nimport RefreshIcon from '@mui/icons-material/Refresh';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ErrorAlert({\n  title = 'Lỗi',\n  message,\n  onRetry,\n  autoHide = false,\n  hideAfter = 10000 // 10 seconds default\n}) {\n  _s();\n  const [open, setOpen] = useState(true);\n\n  // Auto-hide functionality\n  useEffect(() => {\n    let timer;\n    if (autoHide && open) {\n      timer = setTimeout(() => {\n        setOpen(false);\n      }, hideAfter);\n    }\n    return () => {\n      if (timer) clearTimeout(timer);\n    };\n  }, [autoHide, hideAfter, open]);\n\n  // Format error message for better display\n  const formatErrorMessage = msg => {\n    // If message is too long, truncate it\n    if (msg.length > 200) {\n      return msg.substring(0, 200) + '...';\n    }\n    return msg;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      my: 2\n    },\n    children: /*#__PURE__*/_jsxDEV(Collapse, {\n      in: open,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        action: /*#__PURE__*/_jsxDEV(Box, {\n          children: [onRetry && /*#__PURE__*/_jsxDEV(IconButton, {\n            \"aria-label\": \"retry\",\n            color: \"inherit\",\n            size: \"small\",\n            onClick: onRetry,\n            title: \"Th\\u1EED l\\u1EA1i\",\n            children: /*#__PURE__*/_jsxDEV(RefreshIcon, {\n              fontSize: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            \"aria-label\": \"close\",\n            color: \"inherit\",\n            size: \"small\",\n            onClick: () => setOpen(false),\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n              fontSize: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 13\n        }, this),\n        sx: {\n          '& .MuiAlert-message': {\n            wordBreak: 'break-word',\n            whiteSpace: 'pre-wrap'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(AlertTitle, {\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), formatErrorMessage(message)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 5\n  }, this);\n}\n_s(ErrorAlert, \"5swwslfKOPgwwZeTkoqeSpghmqY=\");\n_c = ErrorAlert;\n;\nexport default ErrorAlert;\nvar _c;\n$RefreshReg$(_c, \"ErrorAlert\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Box", "IconButton", "Collapse", "CloseIcon", "RefreshIcon", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title", "message", "onRetry", "autoHide", "hideAfter", "_s", "open", "<PERSON><PERSON><PERSON>", "timer", "setTimeout", "clearTimeout", "formatErrorMessage", "msg", "length", "substring", "sx", "my", "children", "in", "severity", "action", "color", "size", "onClick", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "wordBreak", "whiteSpace", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/common/ErrorAlert.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Alert, AlertTitle, Box, IconButton, Collapse } from '@mui/material';\nimport CloseIcon from '@mui/icons-material/Close';\nimport RefreshIcon from '@mui/icons-material/Refresh';\n\ninterface ErrorAlertProps {\n  title?: string;\n  message: string;\n  onRetry?: () => void;\n  autoHide?: boolean;\n  hideAfter?: number; // in milliseconds\n}\n\nfunction ErrorAlert({\n  title = 'Lỗi',\n  message,\n  onRetry,\n  autoHide = false,\n  hideAfter = 10000 // 10 seconds default\n}: ErrorAlertProps) {\n  const [open, setOpen] = useState(true);\n\n  // Auto-hide functionality\n  useEffect(() => {\n    let timer: NodeJS.Timeout;\n    if (autoHide && open) {\n      timer = setTimeout(() => {\n        setOpen(false);\n      }, hideAfter);\n    }\n\n    return () => {\n      if (timer) clearTimeout(timer);\n    };\n  }, [autoHide, hideAfter, open]);\n\n  // Format error message for better display\n  const formatErrorMessage = (msg: string) => {\n    // If message is too long, truncate it\n    if (msg.length > 200) {\n      return msg.substring(0, 200) + '...';\n    }\n    return msg;\n  };\n\n  return (\n    <Box sx={{ my: 2 }}>\n      <Collapse in={open}>\n        <Alert\n          severity=\"error\"\n          action={\n            <Box>\n              {onRetry && (\n                <IconButton\n                  aria-label=\"retry\"\n                  color=\"inherit\"\n                  size=\"small\"\n                  onClick={onRetry}\n                  title=\"Thử lại\"\n                >\n                  <RefreshIcon fontSize=\"inherit\" />\n                </IconButton>\n              )}\n              <IconButton\n                aria-label=\"close\"\n                color=\"inherit\"\n                size=\"small\"\n                onClick={() => setOpen(false)}\n              >\n                <CloseIcon fontSize=\"inherit\" />\n              </IconButton>\n            </Box>\n          }\n          sx={{\n            '& .MuiAlert-message': {\n              wordBreak: 'break-word',\n              whiteSpace: 'pre-wrap'\n            }\n          }}\n        >\n          <AlertTitle>{title}</AlertTitle>\n          {formatErrorMessage(message)}\n        </Alert>\n      </Collapse>\n    </Box>\n  );\n};\n\nexport default ErrorAlert;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,UAAU,EAAEC,GAAG,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AAC5E,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,WAAW,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUtD,SAASC,UAAUA,CAAC;EAClBC,KAAK,GAAG,KAAK;EACbC,OAAO;EACPC,OAAO;EACPC,QAAQ,GAAG,KAAK;EAChBC,SAAS,GAAG,KAAK,CAAC;AACH,CAAC,EAAE;EAAAC,EAAA;EAClB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;;EAEtC;EACAC,SAAS,CAAC,MAAM;IACd,IAAImB,KAAqB;IACzB,IAAIL,QAAQ,IAAIG,IAAI,EAAE;MACpBE,KAAK,GAAGC,UAAU,CAAC,MAAM;QACvBF,OAAO,CAAC,KAAK,CAAC;MAChB,CAAC,EAAEH,SAAS,CAAC;IACf;IAEA,OAAO,MAAM;MACX,IAAII,KAAK,EAAEE,YAAY,CAACF,KAAK,CAAC;IAChC,CAAC;EACH,CAAC,EAAE,CAACL,QAAQ,EAAEC,SAAS,EAAEE,IAAI,CAAC,CAAC;;EAE/B;EACA,MAAMK,kBAAkB,GAAIC,GAAW,IAAK;IAC1C;IACA,IAAIA,GAAG,CAACC,MAAM,GAAG,GAAG,EAAE;MACpB,OAAOD,GAAG,CAACE,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;IACtC;IACA,OAAOF,GAAG;EACZ,CAAC;EAED,oBACEd,OAAA,CAACN,GAAG;IAACuB,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,eACjBnB,OAAA,CAACJ,QAAQ;MAACwB,EAAE,EAAEZ,IAAK;MAAAW,QAAA,eACjBnB,OAAA,CAACR,KAAK;QACJ6B,QAAQ,EAAC,OAAO;QAChBC,MAAM,eACJtB,OAAA,CAACN,GAAG;UAAAyB,QAAA,GACDf,OAAO,iBACNJ,OAAA,CAACL,UAAU;YACT,cAAW,OAAO;YAClB4B,KAAK,EAAC,SAAS;YACfC,IAAI,EAAC,OAAO;YACZC,OAAO,EAAErB,OAAQ;YACjBF,KAAK,EAAC,mBAAS;YAAAiB,QAAA,eAEfnB,OAAA,CAACF,WAAW;cAAC4B,QAAQ,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CACb,eACD9B,OAAA,CAACL,UAAU;YACT,cAAW,OAAO;YAClB4B,KAAK,EAAC,SAAS;YACfC,IAAI,EAAC,OAAO;YACZC,OAAO,EAAEA,CAAA,KAAMhB,OAAO,CAAC,KAAK,CAAE;YAAAU,QAAA,eAE9BnB,OAAA,CAACH,SAAS;cAAC6B,QAAQ,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACN;QACDb,EAAE,EAAE;UACF,qBAAqB,EAAE;YACrBc,SAAS,EAAE,YAAY;YACvBC,UAAU,EAAE;UACd;QACF,CAAE;QAAAb,QAAA,gBAEFnB,OAAA,CAACP,UAAU;UAAA0B,QAAA,EAAEjB;QAAK;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,EAC/BjB,kBAAkB,CAACV,OAAO,CAAC;MAAA;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV;AAACvB,EAAA,CAzEQN,UAAU;AAAAgC,EAAA,GAAVhC,UAAU;AAyElB;AAED,eAAeA,UAAU;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}