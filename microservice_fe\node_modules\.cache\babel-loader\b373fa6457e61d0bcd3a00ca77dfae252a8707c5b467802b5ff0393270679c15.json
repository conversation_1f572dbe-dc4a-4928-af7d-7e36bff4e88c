{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\components\\\\common\\\\LoadingSpinner.tsx\";\nimport React from 'react';\nimport { CircularProgress, Box } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingSpinner = ({\n  size = 40,\n  color = 'primary',\n  fullScreen = false\n}) => {\n  if (fullScreen) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(255, 255, 255, 0.7)',\n        zIndex: 9999\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: size,\n        color: color\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      justifyContent: 'center',\n      p: 2\n    },\n    children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n      size: size,\n      color: color\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n};\n_c = LoadingSpinner;\nexport default LoadingSpinner;\nvar _c;\n$RefreshReg$(_c, \"LoadingSpinner\");", "map": {"version": 3, "names": ["React", "CircularProgress", "Box", "jsxDEV", "_jsxDEV", "LoadingSpinner", "size", "color", "fullScreen", "sx", "display", "justifyContent", "alignItems", "position", "top", "left", "right", "bottom", "backgroundColor", "zIndex", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/common/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react';\nimport { CircularProgress, Box } from '@mui/material';\n\ninterface LoadingSpinnerProps {\n  size?: number;\n  color?: 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' | 'inherit';\n  fullScreen?: boolean;\n}\n\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ \n  size = 40, \n  color = 'primary',\n  fullScreen = false \n}) => {\n  if (fullScreen) {\n    return (\n      <Box\n        sx={{\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(255, 255, 255, 0.7)',\n          zIndex: 9999,\n        }}\n      >\n        <CircularProgress size={size} color={color} />\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>\n      <CircularProgress size={size} color={color} />\n    </Box>\n  );\n};\n\nexport default LoadingSpinner;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,gBAAgB,EAAEC,GAAG,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQtD,MAAMC,cAA6C,GAAGA,CAAC;EACrDC,IAAI,GAAG,EAAE;EACTC,KAAK,GAAG,SAAS;EACjBC,UAAU,GAAG;AACf,CAAC,KAAK;EACJ,IAAIA,UAAU,EAAE;IACd,oBACEJ,OAAA,CAACF,GAAG;MACFO,EAAE,EAAE;QACFC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,eAAe,EAAE,0BAA0B;QAC3CC,MAAM,EAAE;MACV,CAAE;MAAAC,QAAA,eAEFhB,OAAA,CAACH,gBAAgB;QAACK,IAAI,EAAEA,IAAK;QAACC,KAAK,EAAEA;MAAM;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC;EAEV;EAEA,oBACEpB,OAAA,CAACF,GAAG;IAACO,EAAE,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,cAAc,EAAE,QAAQ;MAAEc,CAAC,EAAE;IAAE,CAAE;IAAAL,QAAA,eAC3DhB,OAAA,CAACH,gBAAgB;MAACK,IAAI,EAAEA,IAAK;MAACC,KAAK,EAAEA;IAAM;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3C,CAAC;AAEV,CAAC;AAACE,EAAA,GA/BIrB,cAA6C;AAiCnD,eAAeA,cAAc;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}