{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\components\\\\common\\\\ConfirmDialog.tsx\";\nimport React from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogContentText, DialogActions, Button } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConfirmDialog = ({\n  open,\n  title,\n  message,\n  onConfirm,\n  onCancel,\n  confirmText = 'Xác nhận',\n  cancelText = 'Hủy',\n  severity = 'warning'\n}) => {\n  const getButtonColor = () => {\n    switch (severity) {\n      case 'error':\n        return 'error';\n      case 'warning':\n        return 'warning';\n      case 'info':\n      default:\n        return 'primary';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onCancel,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onCancel,\n        color: \"inherit\",\n        children: cancelText\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: onConfirm,\n        color: getButtonColor(),\n        variant: \"contained\",\n        autoFocus: true,\n        children: confirmText\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_c = ConfirmDialog;\nexport default ConfirmDialog;\nvar _c;\n$RefreshReg$(_c, \"ConfirmDialog\");", "map": {"version": 3, "names": ["React", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogActions", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ConfirmDialog", "open", "title", "message", "onConfirm", "onCancel", "confirmText", "cancelText", "severity", "getButtonColor", "onClose", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "color", "variant", "autoFocus", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/common/ConfirmDialog.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogContentText,\n  DialogActions,\n  Button,\n} from '@mui/material';\n\ninterface ConfirmDialogProps {\n  open: boolean;\n  title: string;\n  message: string;\n  onConfirm: () => void;\n  onCancel: () => void;\n  confirmText?: string;\n  cancelText?: string;\n  severity?: 'warning' | 'error' | 'info';\n}\n\nconst ConfirmDialog: React.FC<ConfirmDialogProps> = ({\n  open,\n  title,\n  message,\n  onConfirm,\n  onCancel,\n  confirmText = 'Xác nhận',\n  cancelText = 'Hủy',\n  severity = 'warning',\n}): React.ReactNode => {\n  const getButtonColor = () => {\n    switch (severity) {\n      case 'error':\n        return 'error';\n      case 'warning':\n        return 'warning';\n      case 'info':\n      default:\n        return 'primary';\n    }\n  };\n\n  return (\n    <Dialog open={open} onClose={onCancel}>\n      <DialogTitle>{title}</DialogTitle>\n      <DialogContent>\n        <DialogContentText>{message}</DialogContentText>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={onCancel} color=\"inherit\">\n          {cancelText}\n        </Button>\n        <Button onClick={onConfirm} color={getButtonColor()} variant=\"contained\" autoFocus>\n          {confirmText}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default ConfirmDialog;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,iBAAiB,EACjBC,aAAa,EACbC,MAAM,QACD,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAavB,MAAMC,aAA2C,GAAGA,CAAC;EACnDC,IAAI;EACJC,KAAK;EACLC,OAAO;EACPC,SAAS;EACTC,QAAQ;EACRC,WAAW,GAAG,UAAU;EACxBC,UAAU,GAAG,KAAK;EAClBC,QAAQ,GAAG;AACb,CAAC,KAAsB;EACrB,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,QAAQD,QAAQ;MACd,KAAK,OAAO;QACV,OAAO,OAAO;MAChB,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,MAAM;MACX;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,oBACET,OAAA,CAACP,MAAM;IAACS,IAAI,EAAEA,IAAK;IAACS,OAAO,EAAEL,QAAS;IAAAM,QAAA,gBACpCZ,OAAA,CAACN,WAAW;MAAAkB,QAAA,EAAET;IAAK;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc,CAAC,eAClChB,OAAA,CAACL,aAAa;MAAAiB,QAAA,eACZZ,OAAA,CAACJ,iBAAiB;QAAAgB,QAAA,EAAER;MAAO;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAoB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,eAChBhB,OAAA,CAACH,aAAa;MAAAe,QAAA,gBACZZ,OAAA,CAACF,MAAM;QAACmB,OAAO,EAAEX,QAAS;QAACY,KAAK,EAAC,SAAS;QAAAN,QAAA,EACvCJ;MAAU;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACThB,OAAA,CAACF,MAAM;QAACmB,OAAO,EAAEZ,SAAU;QAACa,KAAK,EAAER,cAAc,CAAC,CAAE;QAACS,OAAO,EAAC,WAAW;QAACC,SAAS;QAAAR,QAAA,EAC/EL;MAAW;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACK,EAAA,GAtCIpB,aAA2C;AAwCjD,eAAeA,aAAa;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}