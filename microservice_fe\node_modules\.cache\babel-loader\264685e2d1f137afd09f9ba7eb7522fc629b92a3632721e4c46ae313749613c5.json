{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\components\\\\contract\\\\CustomerContractForm.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, TextField, Button, Typography, Paper, Divider, Stepper, Step, StepLabel, Card, CardContent, IconButton, Tooltip, useTheme } from '@mui/material';\nimport AddIcon from '@mui/icons-material/Add';\nimport PersonIcon from '@mui/icons-material/Person';\nimport DateRangeIcon from '@mui/icons-material/DateRange';\nimport DescriptionIcon from '@mui/icons-material/Description';\nimport MonetizationOnIcon from '@mui/icons-material/MonetizationOn';\nimport HelpOutlineIcon from '@mui/icons-material/HelpOutline';\nimport JobDetailForm from './JobDetailForm';\nimport ContractAmountCalculation from './ContractAmountCalculation';\nimport { <PERSON><PERSON><PERSON><PERSON>, DatePickerField } from '../common';\nimport { CustomerDialog } from '../customer';\nimport { calculateContractAmount, calculateContractDates } from '../../utils/contractCalculationUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CustomerContractForm({\n  contract,\n  onChange,\n  onSubmit,\n  isEdit = false,\n  loading = false\n}) {\n  _s();\n  var _contract$jobDetails;\n  const [customerDialogOpen, setCustomerDialogOpen] = useState(false);\n  const theme = useTheme();\n\n  // Wrapper function to ensure no address field is ever added\n  const safeOnChange = updatedContract => {\n    const cleanContract = {\n      ...updatedContract\n    };\n\n    // Remove address field if it exists\n    delete cleanContract.address;\n    console.log('🧹 SafeOnChange - cleaned contract keys:', Object.keys(cleanContract));\n    if ('address' in updatedContract) {\n      console.warn('⚠️ WARNING: Removed address field in safeOnChange');\n    }\n    onChange(cleanContract);\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Skip address field completely\n    if (name === 'address') {\n      console.warn('⚠️ Ignoring address field input');\n      return;\n    }\n    const updatedContract = {\n      ...contract,\n      [name]: value\n    };\n\n    // Auto-calculate total amount when dates change\n    if (name === 'startingDate' || name === 'endingDate') {\n      const calculation = calculateContractAmount(updatedContract);\n      updatedContract.totalAmount = calculation.totalAmount;\n    }\n\n    // Use safeOnChange to ensure no address field\n    safeOnChange(updatedContract);\n  };\n  const handleFormSubmit = e => {\n    e.preventDefault();\n    if (!loading) {\n      console.log('🚀 Form submission triggered:');\n      console.log('📋 Full contract object:', contract);\n      console.log('📋 Contract keys:', Object.keys(contract));\n\n      // Check if address field exists in form\n      if ('address' in contract) {\n        console.warn('⚠️ WARNING: address field found in form contract!', contract.address);\n      } else {\n        console.log('✅ No address field in form contract');\n      }\n      onSubmit();\n    }\n  };\n  const handleOpenCustomerDialog = () => {\n    setCustomerDialogOpen(true);\n  };\n  const handleCloseCustomerDialog = () => {\n    setCustomerDialogOpen(false);\n  };\n  const handleSelectCustomer = customer => {\n    const updatedContract = {\n      ...contract,\n      customerId: customer.id,\n      customerName: customer.fullName\n    };\n\n    // Use safeOnChange to ensure no address field\n    safeOnChange(updatedContract);\n  };\n  const handleJobDetailChange = (index, jobDetail) => {\n    const updatedJobDetails = [...(contract.jobDetails || [])];\n    updatedJobDetails[index] = jobDetail;\n    const updatedContract = {\n      ...contract,\n      jobDetails: updatedJobDetails\n    };\n\n    // Auto-calculate contract dates from job details\n    const contractDates = calculateContractDates(updatedContract);\n    if (contractDates.startingDate && contractDates.endingDate) {\n      updatedContract.startingDate = contractDates.startingDate;\n      updatedContract.endingDate = contractDates.endingDate;\n    }\n\n    // Auto-calculate total amount when job details change\n    const calculation = calculateContractAmount(updatedContract);\n    updatedContract.totalAmount = calculation.totalAmount;\n\n    // Use safeOnChange to ensure no address field\n    safeOnChange(updatedContract);\n  };\n  const handleAddJobDetail = () => {\n    const newJobDetail = {\n      jobCategoryId: 0,\n      startDate: '',\n      endDate: '',\n      workLocation: '',\n      workShifts: []\n    };\n    const updatedContract = {\n      ...contract,\n      jobDetails: [...(contract.jobDetails || []), newJobDetail]\n    };\n\n    // Use safeOnChange to ensure no address field\n    safeOnChange(updatedContract);\n  };\n  const handleDeleteJobDetail = index => {\n    const updatedJobDetails = [...(contract.jobDetails || [])];\n    updatedJobDetails.splice(index, 1);\n    const updatedContract = {\n      ...contract,\n      jobDetails: updatedJobDetails\n    };\n\n    // Use safeOnChange to ensure no address field\n    safeOnChange(updatedContract);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    component: \"form\",\n    onSubmit: handleFormSubmit,\n    children: [/*#__PURE__*/_jsxDEV(PageHeader, {\n      title: isEdit ? \"Chỉnh sửa Hợp đồng\" : \"Tạo Hợp đồng Mới\",\n      subtitle: \"Nh\\u1EADp th\\xF4ng tin h\\u1EE3p \\u0111\\u1ED3ng b\\xEAn d\\u01B0\\u1EDBi\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Stepper, {\n      activeStep: contract.customerId ? (_contract$jobDetails = contract.jobDetails) !== null && _contract$jobDetails !== void 0 && _contract$jobDetails.length ? 2 : 1 : 0,\n      alternativeLabel: true,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Step, {\n        children: /*#__PURE__*/_jsxDEV(StepLabel, {\n          children: \"Ch\\u1ECDn kh\\xE1ch h\\xE0ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Step, {\n        children: /*#__PURE__*/_jsxDEV(StepLabel, {\n          children: \"Th\\xF4ng tin h\\u1EE3p \\u0111\\u1ED3ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Step, {\n        children: /*#__PURE__*/_jsxDEV(StepLabel, {\n          children: \"Chi ti\\u1EBFt c\\xF4ng vi\\u1EC7c\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 3,\n      sx: {\n        p: 3,\n        mb: 4,\n        border: '1px solid #e0e0e0',\n        borderRadius: '8px',\n        background: theme.palette.background.paper,\n        position: 'relative',\n        overflow: 'hidden',\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '8px',\n          background: theme.palette.primary.main\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          mb: 3,\n          color: theme.palette.primary.main,\n          fontWeight: 'bold'\n        },\n        children: \"H\\u1EE2P \\u0110\\u1ED2NG CUNG C\\u1EA4P D\\u1ECACH V\\u1EE4 NH\\xC2N C\\xD4NG\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: {\n              xs: '100%',\n              md: '48%'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            variant: \"outlined\",\n            sx: {\n              mb: 2,\n              height: '100%'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                sx: {\n                  mb: 2,\n                  fontWeight: 'bold',\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n                  sx: {\n                    mr: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 19\n                }, this), \"Th\\xF4ng tin kh\\xE1ch h\\xE0ng\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this), contract.customerId ? /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 2,\n                  border: '1px dashed #ccc',\n                  borderRadius: '4px',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 'bold'\n                  },\n                  children: contract.customerName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  size: \"small\",\n                  onClick: handleOpenCustomerDialog,\n                  sx: {\n                    mt: 1\n                  },\n                  children: \"Thay \\u0111\\u1ED5i\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                fullWidth: true,\n                onClick: handleOpenCustomerDialog,\n                sx: {\n                  height: 56\n                },\n                startIcon: /*#__PURE__*/_jsxDEV(PersonIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 32\n                }, this),\n                children: \"Ch\\u1ECDn kh\\xE1ch h\\xE0ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: {\n              xs: '100%',\n              md: '48%'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                sx: {\n                  mb: 2,\n                  fontWeight: 'bold',\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(DateRangeIcon, {\n                  sx: {\n                    mr: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 19\n                }, this), \"Th\\u1EDDi gian hi\\u1EC7u l\\u1EF1c h\\u1EE3p \\u0111\\u1ED3ng (T\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh to\\xE1n)\", /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Th\\u1EDDi gian h\\u1EE3p \\u0111\\u1ED3ng \\u0111\\u01B0\\u1EE3c t\\xEDnh t\\u1EF1 \\u0111\\u1ED9ng t\\u1EEB ng\\xE0y b\\u1EAFt \\u0111\\u1EA7u s\\u1EDBm nh\\u1EA5t v\\xE0 ng\\xE0y k\\u1EBFt th\\xFAc mu\\u1ED9n nh\\u1EA5t c\\u1EE7a c\\xE1c c\\xF4ng vi\\u1EC7c\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    sx: {\n                      ml: 1\n                    },\n                    children: /*#__PURE__*/_jsxDEV(HelpOutlineIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 280,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexWrap: 'wrap',\n                  gap: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: {\n                      xs: '100%',\n                      sm: '48%'\n                    }\n                  },\n                  children: /*#__PURE__*/_jsxDEV(DatePickerField, {\n                    label: \"Ng\\xE0y b\\u1EAFt \\u0111\\u1EA7u (T\\u1EF1 \\u0111\\u1ED9ng)\",\n                    value: contract.startingDate || '',\n                    onChange: () => {} // Read-only\n                    ,\n                    required: true,\n                    disabled: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: {\n                      xs: '100%',\n                      sm: '48%'\n                    }\n                  },\n                  children: /*#__PURE__*/_jsxDEV(DatePickerField, {\n                    label: \"Ng\\xE0y k\\u1EBFt th\\xFAc (T\\u1EF1 \\u0111\\u1ED9ng)\",\n                    value: contract.endingDate || '',\n                    onChange: () => {} // Read-only\n                    ,\n                    required: true,\n                    disabled: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: {\n              xs: '100%',\n              md: '48%'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                sx: {\n                  mb: 2,\n                  fontWeight: 'bold',\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(MonetizationOnIcon, {\n                  sx: {\n                    mr: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 19\n                }, this), \"Gi\\xE1 tr\\u1ECB h\\u1EE3p \\u0111\\u1ED3ng (T\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh to\\xE1n)\", /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"T\\u1ED5ng gi\\xE1 tr\\u1ECB h\\u1EE3p \\u0111\\u1ED3ng \\u0111\\u01B0\\u1EE3c t\\xEDnh t\\u1EF1 \\u0111\\u1ED9ng d\\u1EF1a tr\\xEAn l\\u01B0\\u01A1ng, s\\u1ED1 ng\\u01B0\\u1EDDi v\\xE0 s\\u1ED1 ng\\xE0y l\\xE0m vi\\u1EC7c\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    sx: {\n                      ml: 1\n                    },\n                    children: /*#__PURE__*/_jsxDEV(HelpOutlineIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 317,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"T\\u1ED5ng gi\\xE1 tr\\u1ECB h\\u1EE3p \\u0111\\u1ED3ng (VN\\u0110)\",\n                name: \"totalAmount\",\n                type: \"text\",\n                value: contract.totalAmount ? contract.totalAmount.toLocaleString('vi-VN') + ' VNĐ' : '0 VNĐ',\n                slotProps: {\n                  input: {\n                    readOnly: true\n                  }\n                },\n                sx: {\n                  '& input': {\n                    fontWeight: 'bold',\n                    color: theme.palette.success.main,\n                    backgroundColor: theme.palette.action.hover\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: '100%'\n          },\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                sx: {\n                  mb: 2,\n                  fontWeight: 'bold',\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(DescriptionIcon, {\n                  sx: {\n                    mr: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 19\n                }, this), \"M\\xF4 t\\u1EA3 h\\u1EE3p \\u0111\\u1ED3ng\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"M\\xF4 t\\u1EA3 chi ti\\u1EBFt v\\u1EC1 h\\u1EE3p \\u0111\\u1ED3ng\",\n                name: \"description\",\n                value: contract.description || '',\n                onChange: handleInputChange,\n                onKeyDown: e => {\n                  // Prevent Enter from submitting in multiline text field\n                  if (e.key === 'Enter' && !e.shiftKey) {\n                    e.stopPropagation();\n                  }\n                },\n                multiline: true,\n                rows: 3,\n                placeholder: \"Nh\\u1EADp c\\xE1c th\\xF4ng tin b\\u1ED5 sung v\\u1EC1 h\\u1EE3p \\u0111\\u1ED3ng (kh\\xF4ng b\\u1EAFt bu\\u1ED9c)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CustomerDialog, {\n        open: customerDialogOpen,\n        onClose: handleCloseCustomerDialog,\n        onSelectCustomer: handleSelectCustomer\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      sx: {\n        mb: 2,\n        mt: 4,\n        fontWeight: 'bold',\n        color: theme.palette.primary.main\n      },\n      children: \"CHI TI\\u1EBET C\\xD4NG VI\\u1EC6C\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 7\n    }, this), (contract.jobDetails || []).map((jobDetail, index) => /*#__PURE__*/_jsxDEV(JobDetailForm, {\n      jobDetail: jobDetail,\n      onChange: updatedJobDetail => handleJobDetailChange(index, updatedJobDetail),\n      onDelete: () => handleDeleteJobDetail(index),\n      showDelete: true\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 9\n    }, this)), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"outlined\",\n      startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 20\n      }, this),\n      onClick: handleAddJobDetail,\n      sx: {\n        mb: 3\n      },\n      children: \"Th\\xEAm c\\xF4ng vi\\u1EC7c\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ContractAmountCalculation, {\n      contract: contract\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        my: 3\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 407,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'flex-end',\n        mt: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"submit\",\n        variant: \"contained\",\n        color: \"primary\",\n        size: \"large\",\n        disabled: loading || !contract.customerId || contract.customerId === 0,\n        onClick: () => {\n          var _contract$jobDetails2;\n          console.log('🔍 Button clicked - Contract state:', {\n            customerId: contract.customerId,\n            customerIdType: typeof contract.customerId,\n            jobDetailsCount: ((_contract$jobDetails2 = contract.jobDetails) === null || _contract$jobDetails2 === void 0 ? void 0 : _contract$jobDetails2.length) || 0,\n            loading: loading,\n            buttonDisabled: loading || !contract.customerId || contract.customerId === 0\n          });\n        },\n        children: loading ? 'Đang xử lý...' : isEdit ? 'Cập nhật Hợp đồng' : 'Tạo Hợp đồng'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 180,\n    columnNumber: 5\n  }, this);\n}\n_s(CustomerContractForm, \"Rn1m6oxPnssLvBhOWFQMK5y6/gU=\", false, function () {\n  return [useTheme];\n});\n_c = CustomerContractForm;\n;\nexport default CustomerContractForm;\nvar _c;\n$RefreshReg$(_c, \"CustomerContractForm\");", "map": {"version": 3, "names": ["React", "useState", "Box", "TextField", "<PERSON><PERSON>", "Typography", "Paper", "Divider", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "IconButton", "<PERSON><PERSON><PERSON>", "useTheme", "AddIcon", "PersonIcon", "DateRangeIcon", "DescriptionIcon", "MonetizationOnIcon", "HelpOutlineIcon", "JobDetailForm", "ContractAmountCalculation", "<PERSON><PERSON><PERSON><PERSON>", "DatePickerField", "CustomerDialog", "calculateContractAmount", "calculateContractDates", "jsxDEV", "_jsxDEV", "CustomerContractForm", "contract", "onChange", "onSubmit", "isEdit", "loading", "_s", "_contract$jobDetails", "customerDialogOpen", "setCustomerDialogOpen", "theme", "safeOnChange", "updatedContract", "cleanContract", "address", "console", "log", "Object", "keys", "warn", "handleInputChange", "e", "name", "value", "target", "calculation", "totalAmount", "handleFormSubmit", "preventDefault", "handleOpenCustomerDialog", "handleCloseCustomerDialog", "handleSelectCustomer", "customer", "customerId", "id", "customerName", "fullName", "handleJobDetailChange", "index", "jobDetail", "updatedJobDetails", "jobDetails", "contractDates", "startingDate", "endingDate", "handleAddJobDetail", "newJobDetail", "jobCategoryId", "startDate", "endDate", "workLocation", "workShifts", "handleDeleteJobDetail", "splice", "component", "children", "title", "subtitle", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "activeStep", "length", "alternativeLabel", "sx", "mb", "elevation", "p", "border", "borderRadius", "background", "palette", "paper", "position", "overflow", "content", "top", "left", "width", "height", "primary", "main", "variant", "color", "fontWeight", "display", "flexWrap", "gap", "xs", "md", "alignItems", "mr", "size", "onClick", "mt", "fullWidth", "startIcon", "ml", "fontSize", "sm", "label", "required", "disabled", "type", "toLocaleString", "slotProps", "input", "readOnly", "success", "backgroundColor", "action", "hover", "description", "onKeyDown", "key", "shift<PERSON>ey", "stopPropagation", "multiline", "rows", "placeholder", "open", "onClose", "onSelectCustomer", "map", "updatedJobDetail", "onDelete", "showDelete", "my", "justifyContent", "_contract$jobDetails2", "customerIdType", "jobDetailsCount", "buttonDisabled", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/contract/CustomerContractForm.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  Typography,\n  Paper,\n  Divider,\n  <PERSON>per,\n  <PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  Card,\n  CardContent,\n  IconButton,\n  Tooltip,\n  useTheme,\n} from '@mui/material';\nimport AddIcon from '@mui/icons-material/Add';\nimport PersonIcon from '@mui/icons-material/Person';\nimport DateRangeIcon from '@mui/icons-material/DateRange';\nimport DescriptionIcon from '@mui/icons-material/Description';\nimport MonetizationOnIcon from '@mui/icons-material/MonetizationOn';\nimport HelpOutlineIcon from '@mui/icons-material/HelpOutline';\nimport { CustomerContract, Customer, JobDetail } from '../../models';\nimport JobDetailForm from './JobDetailForm';\nimport ContractAmountCalculation from './ContractAmountCalculation';\nimport { PageHeader, DatePickerField } from '../common';\nimport { CustomerDialog } from '../customer';\nimport { calculateContractAmount, calculateContractDates } from '../../utils/contractCalculationUtils';\n\ninterface CustomerContractFormProps {\n  contract: Partial<CustomerContract>;\n  onChange: (contract: Partial<CustomerContract>) => void;\n  onSubmit: () => void;\n  isEdit?: boolean;\n  loading?: boolean;\n}\n\nfunction CustomerContractForm({\n  contract,\n  onChange,\n  onSubmit,\n  isEdit = false,\n  loading = false,\n}: CustomerContractFormProps) {\n  const [customerDialogOpen, setCustomerDialogOpen] = useState(false);\n  const theme = useTheme();\n\n  // Wrapper function to ensure no address field is ever added\n  const safeOnChange = (updatedContract: Partial<CustomerContract>) => {\n    const cleanContract = { ...updatedContract };\n\n    // Remove address field if it exists\n    delete (cleanContract as any).address;\n\n    console.log('🧹 SafeOnChange - cleaned contract keys:', Object.keys(cleanContract));\n    if ('address' in updatedContract) {\n      console.warn('⚠️ WARNING: Removed address field in safeOnChange');\n    }\n\n    onChange(cleanContract);\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n\n    // Skip address field completely\n    if (name === 'address') {\n      console.warn('⚠️ Ignoring address field input');\n      return;\n    }\n\n    const updatedContract = {\n      ...contract,\n      [name]: value,\n    };\n\n    // Auto-calculate total amount when dates change\n    if (name === 'startingDate' || name === 'endingDate') {\n      const calculation = calculateContractAmount(updatedContract);\n      updatedContract.totalAmount = calculation.totalAmount;\n    }\n\n    // Use safeOnChange to ensure no address field\n    safeOnChange(updatedContract);\n  };\n\n  const handleFormSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!loading) {\n      console.log('🚀 Form submission triggered:');\n      console.log('📋 Full contract object:', contract);\n      console.log('📋 Contract keys:', Object.keys(contract));\n\n      // Check if address field exists in form\n      if ('address' in contract) {\n        console.warn('⚠️ WARNING: address field found in form contract!', contract.address);\n      } else {\n        console.log('✅ No address field in form contract');\n      }\n      onSubmit();\n    }\n  };\n\n  const handleOpenCustomerDialog = () => {\n    setCustomerDialogOpen(true);\n  };\n\n  const handleCloseCustomerDialog = () => {\n    setCustomerDialogOpen(false);\n  };\n\n  const handleSelectCustomer = (customer: Customer) => {\n    const updatedContract = {\n      ...contract,\n      customerId: customer.id,\n      customerName: customer.fullName,\n    };\n\n    // Use safeOnChange to ensure no address field\n    safeOnChange(updatedContract);\n  };\n\n  const handleJobDetailChange = (index: number, jobDetail: Partial<JobDetail>) => {\n    const updatedJobDetails = [...(contract.jobDetails || [])];\n    updatedJobDetails[index] = jobDetail as JobDetail;\n\n    const updatedContract = {\n      ...contract,\n      jobDetails: updatedJobDetails,\n    };\n\n    // Auto-calculate contract dates from job details\n    const contractDates = calculateContractDates(updatedContract);\n    if (contractDates.startingDate && contractDates.endingDate) {\n      updatedContract.startingDate = contractDates.startingDate;\n      updatedContract.endingDate = contractDates.endingDate;\n    }\n\n    // Auto-calculate total amount when job details change\n    const calculation = calculateContractAmount(updatedContract);\n    updatedContract.totalAmount = calculation.totalAmount;\n\n    // Use safeOnChange to ensure no address field\n    safeOnChange(updatedContract);\n  };\n\n  const handleAddJobDetail = () => {\n    const newJobDetail: JobDetail = {\n      jobCategoryId: 0,\n      startDate: '',\n      endDate: '',\n      workLocation: '',\n      workShifts: [],\n    };\n\n    const updatedContract = {\n      ...contract,\n      jobDetails: [...(contract.jobDetails || []), newJobDetail],\n    };\n\n    // Use safeOnChange to ensure no address field\n    safeOnChange(updatedContract);\n  };\n\n  const handleDeleteJobDetail = (index: number) => {\n    const updatedJobDetails = [...(contract.jobDetails || [])];\n    updatedJobDetails.splice(index, 1);\n\n    const updatedContract = {\n      ...contract,\n      jobDetails: updatedJobDetails,\n    };\n\n    // Use safeOnChange to ensure no address field\n    safeOnChange(updatedContract);\n  };\n\n  return (\n    <Box component=\"form\" onSubmit={handleFormSubmit}>\n      <PageHeader\n        title={isEdit ? \"Chỉnh sửa Hợp đồng\" : \"Tạo Hợp đồng Mới\"}\n        subtitle=\"Nhập thông tin hợp đồng bên dưới\"\n      />\n\n      {/* Contract workflow steps */}\n      <Stepper\n        activeStep={contract.customerId ? (contract.jobDetails?.length ? 2 : 1) : 0}\n        alternativeLabel\n        sx={{ mb: 4 }}\n      >\n        <Step>\n          <StepLabel>Chọn khách hàng</StepLabel>\n        </Step>\n        <Step>\n          <StepLabel>Thông tin hợp đồng</StepLabel>\n        </Step>\n        <Step>\n          <StepLabel>Chi tiết công việc</StepLabel>\n        </Step>\n      </Stepper>\n\n      {/* Contract header with customer selection */}\n      <Paper\n        elevation={3}\n        sx={{\n          p: 3,\n          mb: 4,\n          border: '1px solid #e0e0e0',\n          borderRadius: '8px',\n          background: theme.palette.background.paper,\n          position: 'relative',\n          overflow: 'hidden',\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            width: '100%',\n            height: '8px',\n            background: theme.palette.primary.main,\n          }\n        }}\n      >\n        <Typography variant=\"h5\" sx={{ mb: 3, color: theme.palette.primary.main, fontWeight: 'bold' }}>\n          HỢP ĐỒNG CUNG CẤP DỊCH VỤ NHÂN CÔNG\n        </Typography>\n\n        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>\n          {/* Customer selection section */}\n          <Box sx={{ width: { xs: '100%', md: '48%' } }}>\n            <Card variant=\"outlined\" sx={{ mb: 2, height: '100%' }}>\n              <CardContent>\n                <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>\n                  <PersonIcon sx={{ mr: 1 }} />\n                  Thông tin khách hàng\n                </Typography>\n\n                {contract.customerId ? (\n                  <Box sx={{ p: 2, border: '1px dashed #ccc', borderRadius: '4px', mb: 2 }}>\n                    <Typography variant=\"body1\" sx={{ fontWeight: 'bold' }}>\n                      {contract.customerName}\n                    </Typography>\n                    <Button\n                      variant=\"outlined\"\n                      size=\"small\"\n                      onClick={handleOpenCustomerDialog}\n                      sx={{ mt: 1 }}\n                    >\n                      Thay đổi\n                    </Button>\n                  </Box>\n                ) : (\n                  <Button\n                    variant=\"contained\"\n                    color=\"primary\"\n                    fullWidth\n                    onClick={handleOpenCustomerDialog}\n                    sx={{ height: 56 }}\n                    startIcon={<PersonIcon />}\n                  >\n                    Chọn khách hàng\n                  </Button>\n                )}\n              </CardContent>\n            </Card>\n          </Box>\n\n\n\n          {/* Contract dates */}\n          <Box sx={{ width: { xs: '100%', md: '48%' } }}>\n            <Card variant=\"outlined\" sx={{ mb: 2 }}>\n              <CardContent>\n                <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>\n                  <DateRangeIcon sx={{ mr: 1 }} />\n                  Thời gian hiệu lực hợp đồng (Tự động tính toán)\n                  <Tooltip title=\"Thời gian hợp đồng được tính tự động từ ngày bắt đầu sớm nhất và ngày kết thúc muộn nhất của các công việc\">\n                    <IconButton size=\"small\" sx={{ ml: 1 }}>\n                      <HelpOutlineIcon fontSize=\"small\" />\n                    </IconButton>\n                  </Tooltip>\n                </Typography>\n                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>\n                  <Box sx={{ width: { xs: '100%', sm: '48%' } }}>\n                    <DatePickerField\n                      label=\"Ngày bắt đầu (Tự động)\"\n                      value={contract.startingDate || ''}\n                      onChange={() => {}} // Read-only\n                      required\n                      disabled\n                    />\n                  </Box>\n                  <Box sx={{ width: { xs: '100%', sm: '48%' } }}>\n                    <DatePickerField\n                      label=\"Ngày kết thúc (Tự động)\"\n                      value={contract.endingDate || ''}\n                      onChange={() => {}} // Read-only\n                      required\n                      disabled\n                    />\n                  </Box>\n                </Box>\n              </CardContent>\n            </Card>\n          </Box>\n\n          {/* Contract value - Auto calculated */}\n          <Box sx={{ width: { xs: '100%', md: '48%' } }}>\n            <Card variant=\"outlined\" sx={{ mb: 2 }}>\n              <CardContent>\n                <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>\n                  <MonetizationOnIcon sx={{ mr: 1 }} />\n                  Giá trị hợp đồng (Tự động tính toán)\n                  <Tooltip title=\"Tổng giá trị hợp đồng được tính tự động dựa trên lương, số người và số ngày làm việc\">\n                    <IconButton size=\"small\" sx={{ ml: 1 }}>\n                      <HelpOutlineIcon fontSize=\"small\" />\n                    </IconButton>\n                  </Tooltip>\n                </Typography>\n                <TextField\n                  fullWidth\n                  label=\"Tổng giá trị hợp đồng (VNĐ)\"\n                  name=\"totalAmount\"\n                  type=\"text\"\n                  value={contract.totalAmount ? contract.totalAmount.toLocaleString('vi-VN') + ' VNĐ' : '0 VNĐ'}\n                  slotProps={{\n                    input: {\n                      readOnly: true,\n                    },\n                  }}\n                  sx={{\n                    '& input': {\n                      fontWeight: 'bold',\n                      color: theme.palette.success.main,\n                      backgroundColor: theme.palette.action.hover\n                    }\n                  }}\n                />\n              </CardContent>\n            </Card>\n          </Box>\n\n          {/* Description */}\n          <Box sx={{ width: '100%' }}>\n            <Card variant=\"outlined\" sx={{ mb: 2 }}>\n              <CardContent>\n                <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>\n                  <DescriptionIcon sx={{ mr: 1 }} />\n                  Mô tả hợp đồng\n                </Typography>\n                <TextField\n                  fullWidth\n                  label=\"Mô tả chi tiết về hợp đồng\"\n                  name=\"description\"\n                  value={contract.description || ''}\n                  onChange={handleInputChange}\n                  onKeyDown={(e) => {\n                    // Prevent Enter from submitting in multiline text field\n                    if (e.key === 'Enter' && !e.shiftKey) {\n                      e.stopPropagation();\n                    }\n                  }}\n                  multiline\n                  rows={3}\n                  placeholder=\"Nhập các thông tin bổ sung về hợp đồng (không bắt buộc)\"\n                />\n              </CardContent>\n            </Card>\n          </Box>\n        </Box>\n\n        <CustomerDialog\n          open={customerDialogOpen}\n          onClose={handleCloseCustomerDialog}\n          onSelectCustomer={handleSelectCustomer}\n        />\n      </Paper>\n\n      {/* Job details section */}\n      <Typography variant=\"h5\" sx={{ mb: 2, mt: 4, fontWeight: 'bold', color: theme.palette.primary.main }}>\n        CHI TIẾT CÔNG VIỆC\n      </Typography>\n\n      {(contract.jobDetails || []).map((jobDetail, index) => (\n        <JobDetailForm\n          key={index}\n          jobDetail={jobDetail}\n          onChange={(updatedJobDetail) => handleJobDetailChange(index, updatedJobDetail)}\n          onDelete={() => handleDeleteJobDetail(index)}\n          showDelete={true}\n        />\n      ))}\n\n      <Button\n        variant=\"outlined\"\n        startIcon={<AddIcon />}\n        onClick={handleAddJobDetail}\n        sx={{ mb: 3 }}\n      >\n        Thêm công việc\n      </Button>\n\n      {/* Contract Amount Calculation */}\n      <ContractAmountCalculation contract={contract} />\n\n      <Divider sx={{ my: 3 }} />\n\n      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>\n        <Button\n          type=\"submit\"\n          variant=\"contained\"\n          color=\"primary\"\n          size=\"large\"\n          disabled={loading || !contract.customerId || contract.customerId === 0}\n          onClick={() => {\n            console.log('🔍 Button clicked - Contract state:', {\n              customerId: contract.customerId,\n              customerIdType: typeof contract.customerId,\n              jobDetailsCount: contract.jobDetails?.length || 0,\n              loading: loading,\n              buttonDisabled: loading || !contract.customerId || contract.customerId === 0\n            });\n          }}\n        >\n          {loading ? 'Đang xử lý...' : (isEdit ? 'Cập nhật Hợp đồng' : 'Tạo Hợp đồng')}\n        </Button>\n      </Box>\n    </Box>\n  );\n};\n\nexport default CustomerContractForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,OAAO,EACPC,QAAQ,QACH,eAAe;AACtB,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,eAAe,MAAM,iCAAiC;AAE7D,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,yBAAyB,MAAM,6BAA6B;AACnE,SAASC,UAAU,EAAEC,eAAe,QAAQ,WAAW;AACvD,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,uBAAuB,EAAEC,sBAAsB,QAAQ,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUvG,SAASC,oBAAoBA,CAAC;EAC5BC,QAAQ;EACRC,QAAQ;EACRC,QAAQ;EACRC,MAAM,GAAG,KAAK;EACdC,OAAO,GAAG;AACe,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,oBAAA;EAC5B,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAMwC,KAAK,GAAG1B,QAAQ,CAAC,CAAC;;EAExB;EACA,MAAM2B,YAAY,GAAIC,eAA0C,IAAK;IACnE,MAAMC,aAAa,GAAG;MAAE,GAAGD;IAAgB,CAAC;;IAE5C;IACA,OAAQC,aAAa,CAASC,OAAO;IAErCC,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEC,MAAM,CAACC,IAAI,CAACL,aAAa,CAAC,CAAC;IACnF,IAAI,SAAS,IAAID,eAAe,EAAE;MAChCG,OAAO,CAACI,IAAI,CAAC,mDAAmD,CAAC;IACnE;IAEAjB,QAAQ,CAACW,aAAa,CAAC;EACzB,CAAC;EAED,MAAMO,iBAAiB,GAAIC,CAAsC,IAAK;IACpE,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACA,IAAIF,IAAI,KAAK,SAAS,EAAE;MACtBP,OAAO,CAACI,IAAI,CAAC,iCAAiC,CAAC;MAC/C;IACF;IAEA,MAAMP,eAAe,GAAG;MACtB,GAAGX,QAAQ;MACX,CAACqB,IAAI,GAAGC;IACV,CAAC;;IAED;IACA,IAAID,IAAI,KAAK,cAAc,IAAIA,IAAI,KAAK,YAAY,EAAE;MACpD,MAAMG,WAAW,GAAG7B,uBAAuB,CAACgB,eAAe,CAAC;MAC5DA,eAAe,CAACc,WAAW,GAAGD,WAAW,CAACC,WAAW;IACvD;;IAEA;IACAf,YAAY,CAACC,eAAe,CAAC;EAC/B,CAAC;EAED,MAAMe,gBAAgB,GAAIN,CAAkB,IAAK;IAC/CA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClB,IAAI,CAACvB,OAAO,EAAE;MACZU,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5CD,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEf,QAAQ,CAAC;MACjDc,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEC,MAAM,CAACC,IAAI,CAACjB,QAAQ,CAAC,CAAC;;MAEvD;MACA,IAAI,SAAS,IAAIA,QAAQ,EAAE;QACzBc,OAAO,CAACI,IAAI,CAAC,mDAAmD,EAAElB,QAAQ,CAACa,OAAO,CAAC;MACrF,CAAC,MAAM;QACLC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MACpD;MACAb,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EAED,MAAM0B,wBAAwB,GAAGA,CAAA,KAAM;IACrCpB,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMqB,yBAAyB,GAAGA,CAAA,KAAM;IACtCrB,qBAAqB,CAAC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAMsB,oBAAoB,GAAIC,QAAkB,IAAK;IACnD,MAAMpB,eAAe,GAAG;MACtB,GAAGX,QAAQ;MACXgC,UAAU,EAAED,QAAQ,CAACE,EAAE;MACvBC,YAAY,EAAEH,QAAQ,CAACI;IACzB,CAAC;;IAED;IACAzB,YAAY,CAACC,eAAe,CAAC;EAC/B,CAAC;EAED,MAAMyB,qBAAqB,GAAGA,CAACC,KAAa,EAAEC,SAA6B,KAAK;IAC9E,MAAMC,iBAAiB,GAAG,CAAC,IAAIvC,QAAQ,CAACwC,UAAU,IAAI,EAAE,CAAC,CAAC;IAC1DD,iBAAiB,CAACF,KAAK,CAAC,GAAGC,SAAsB;IAEjD,MAAM3B,eAAe,GAAG;MACtB,GAAGX,QAAQ;MACXwC,UAAU,EAAED;IACd,CAAC;;IAED;IACA,MAAME,aAAa,GAAG7C,sBAAsB,CAACe,eAAe,CAAC;IAC7D,IAAI8B,aAAa,CAACC,YAAY,IAAID,aAAa,CAACE,UAAU,EAAE;MAC1DhC,eAAe,CAAC+B,YAAY,GAAGD,aAAa,CAACC,YAAY;MACzD/B,eAAe,CAACgC,UAAU,GAAGF,aAAa,CAACE,UAAU;IACvD;;IAEA;IACA,MAAMnB,WAAW,GAAG7B,uBAAuB,CAACgB,eAAe,CAAC;IAC5DA,eAAe,CAACc,WAAW,GAAGD,WAAW,CAACC,WAAW;;IAErD;IACAf,YAAY,CAACC,eAAe,CAAC;EAC/B,CAAC;EAED,MAAMiC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,YAAuB,GAAG;MAC9BC,aAAa,EAAE,CAAC;MAChBC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,UAAU,EAAE;IACd,CAAC;IAED,MAAMvC,eAAe,GAAG;MACtB,GAAGX,QAAQ;MACXwC,UAAU,EAAE,CAAC,IAAIxC,QAAQ,CAACwC,UAAU,IAAI,EAAE,CAAC,EAAEK,YAAY;IAC3D,CAAC;;IAED;IACAnC,YAAY,CAACC,eAAe,CAAC;EAC/B,CAAC;EAED,MAAMwC,qBAAqB,GAAId,KAAa,IAAK;IAC/C,MAAME,iBAAiB,GAAG,CAAC,IAAIvC,QAAQ,CAACwC,UAAU,IAAI,EAAE,CAAC,CAAC;IAC1DD,iBAAiB,CAACa,MAAM,CAACf,KAAK,EAAE,CAAC,CAAC;IAElC,MAAM1B,eAAe,GAAG;MACtB,GAAGX,QAAQ;MACXwC,UAAU,EAAED;IACd,CAAC;;IAED;IACA7B,YAAY,CAACC,eAAe,CAAC;EAC/B,CAAC;EAED,oBACEb,OAAA,CAAC5B,GAAG;IAACmF,SAAS,EAAC,MAAM;IAACnD,QAAQ,EAAEwB,gBAAiB;IAAA4B,QAAA,gBAC/CxD,OAAA,CAACN,UAAU;MACT+D,KAAK,EAAEpD,MAAM,GAAG,oBAAoB,GAAG,kBAAmB;MAC1DqD,QAAQ,EAAC;IAAkC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,eAGF9D,OAAA,CAACtB,OAAO;MACNqF,UAAU,EAAE7D,QAAQ,CAACgC,UAAU,GAAI,CAAA1B,oBAAA,GAAAN,QAAQ,CAACwC,UAAU,cAAAlC,oBAAA,eAAnBA,oBAAA,CAAqBwD,MAAM,GAAG,CAAC,GAAG,CAAC,GAAI,CAAE;MAC5EC,gBAAgB;MAChBC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,gBAEdxD,OAAA,CAACrB,IAAI;QAAA6E,QAAA,eACHxD,OAAA,CAACpB,SAAS;UAAA4E,QAAA,EAAC;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACP9D,OAAA,CAACrB,IAAI;QAAA6E,QAAA,eACHxD,OAAA,CAACpB,SAAS;UAAA4E,QAAA,EAAC;QAAkB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACP9D,OAAA,CAACrB,IAAI;QAAA6E,QAAA,eACHxD,OAAA,CAACpB,SAAS;UAAA4E,QAAA,EAAC;QAAkB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGV9D,OAAA,CAACxB,KAAK;MACJ4F,SAAS,EAAE,CAAE;MACbF,EAAE,EAAE;QACFG,CAAC,EAAE,CAAC;QACJF,EAAE,EAAE,CAAC;QACLG,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE7D,KAAK,CAAC8D,OAAO,CAACD,UAAU,CAACE,KAAK;QAC1CC,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,QAAQ;QAClB,WAAW,EAAE;UACXC,OAAO,EAAE,IAAI;UACbF,QAAQ,EAAE,UAAU;UACpBG,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,KAAK;UACbT,UAAU,EAAE7D,KAAK,CAAC8D,OAAO,CAACS,OAAO,CAACC;QACpC;MACF,CAAE;MAAA3B,QAAA,gBAEFxD,OAAA,CAACzB,UAAU;QAAC6G,OAAO,EAAC,IAAI;QAAClB,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEkB,KAAK,EAAE1E,KAAK,CAAC8D,OAAO,CAACS,OAAO,CAACC,IAAI;UAAEG,UAAU,EAAE;QAAO,CAAE;QAAA9B,QAAA,EAAC;MAE/F;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb9D,OAAA,CAAC5B,GAAG;QAAC8F,EAAE,EAAE;UAAEqB,OAAO,EAAE,MAAM;UAAEC,QAAQ,EAAE,MAAM;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAjC,QAAA,gBAErDxD,OAAA,CAAC5B,GAAG;UAAC8F,EAAE,EAAE;YAAEc,KAAK,EAAE;cAAEU,EAAE,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAM;UAAE,CAAE;UAAAnC,QAAA,eAC5CxD,OAAA,CAACnB,IAAI;YAACuG,OAAO,EAAC,UAAU;YAAClB,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEc,MAAM,EAAE;YAAO,CAAE;YAAAzB,QAAA,eACrDxD,OAAA,CAAClB,WAAW;cAAA0E,QAAA,gBACVxD,OAAA,CAACzB,UAAU;gBAAC6G,OAAO,EAAC,WAAW;gBAAClB,EAAE,EAAE;kBAAEC,EAAE,EAAE,CAAC;kBAAEmB,UAAU,EAAE,MAAM;kBAAEC,OAAO,EAAE,MAAM;kBAAEK,UAAU,EAAE;gBAAS,CAAE;gBAAApC,QAAA,gBACvGxD,OAAA,CAACb,UAAU;kBAAC+E,EAAE,EAAE;oBAAE2B,EAAE,EAAE;kBAAE;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,iCAE/B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EAEZ5D,QAAQ,CAACgC,UAAU,gBAClBlC,OAAA,CAAC5B,GAAG;gBAAC8F,EAAE,EAAE;kBAAEG,CAAC,EAAE,CAAC;kBAAEC,MAAM,EAAE,iBAAiB;kBAAEC,YAAY,EAAE,KAAK;kBAAEJ,EAAE,EAAE;gBAAE,CAAE;gBAAAX,QAAA,gBACvExD,OAAA,CAACzB,UAAU;kBAAC6G,OAAO,EAAC,OAAO;kBAAClB,EAAE,EAAE;oBAAEoB,UAAU,EAAE;kBAAO,CAAE;kBAAA9B,QAAA,EACpDtD,QAAQ,CAACkC;gBAAY;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACb9D,OAAA,CAAC1B,MAAM;kBACL8G,OAAO,EAAC,UAAU;kBAClBU,IAAI,EAAC,OAAO;kBACZC,OAAO,EAAEjE,wBAAyB;kBAClCoC,EAAE,EAAE;oBAAE8B,EAAE,EAAE;kBAAE,CAAE;kBAAAxC,QAAA,EACf;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,gBAEN9D,OAAA,CAAC1B,MAAM;gBACL8G,OAAO,EAAC,WAAW;gBACnBC,KAAK,EAAC,SAAS;gBACfY,SAAS;gBACTF,OAAO,EAAEjE,wBAAyB;gBAClCoC,EAAE,EAAE;kBAAEe,MAAM,EAAE;gBAAG,CAAE;gBACnBiB,SAAS,eAAElG,OAAA,CAACb,UAAU;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAN,QAAA,EAC3B;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAKN9D,OAAA,CAAC5B,GAAG;UAAC8F,EAAE,EAAE;YAAEc,KAAK,EAAE;cAAEU,EAAE,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAM;UAAE,CAAE;UAAAnC,QAAA,eAC5CxD,OAAA,CAACnB,IAAI;YAACuG,OAAO,EAAC,UAAU;YAAClB,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAX,QAAA,eACrCxD,OAAA,CAAClB,WAAW;cAAA0E,QAAA,gBACVxD,OAAA,CAACzB,UAAU;gBAAC6G,OAAO,EAAC,WAAW;gBAAClB,EAAE,EAAE;kBAAEC,EAAE,EAAE,CAAC;kBAAEmB,UAAU,EAAE,MAAM;kBAAEC,OAAO,EAAE,MAAM;kBAAEK,UAAU,EAAE;gBAAS,CAAE;gBAAApC,QAAA,gBACvGxD,OAAA,CAACZ,aAAa;kBAAC8E,EAAE,EAAE;oBAAE2B,EAAE,EAAE;kBAAE;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,sGAEhC,eAAA9D,OAAA,CAAChB,OAAO;kBAACyE,KAAK,EAAC,0OAA4G;kBAAAD,QAAA,eACzHxD,OAAA,CAACjB,UAAU;oBAAC+G,IAAI,EAAC,OAAO;oBAAC5B,EAAE,EAAE;sBAAEiC,EAAE,EAAE;oBAAE,CAAE;oBAAA3C,QAAA,eACrCxD,OAAA,CAACT,eAAe;sBAAC6G,QAAQ,EAAC;oBAAO;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACb9D,OAAA,CAAC5B,GAAG;gBAAC8F,EAAE,EAAE;kBAAEqB,OAAO,EAAE,MAAM;kBAAEC,QAAQ,EAAE,MAAM;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,gBACrDxD,OAAA,CAAC5B,GAAG;kBAAC8F,EAAE,EAAE;oBAAEc,KAAK,EAAE;sBAAEU,EAAE,EAAE,MAAM;sBAAEW,EAAE,EAAE;oBAAM;kBAAE,CAAE;kBAAA7C,QAAA,eAC5CxD,OAAA,CAACL,eAAe;oBACd2G,KAAK,EAAC,yDAAwB;oBAC9B9E,KAAK,EAAEtB,QAAQ,CAAC0C,YAAY,IAAI,EAAG;oBACnCzC,QAAQ,EAAEA,CAAA,KAAM,CAAC,CAAE,CAAC;oBAAA;oBACpBoG,QAAQ;oBACRC,QAAQ;kBAAA;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN9D,OAAA,CAAC5B,GAAG;kBAAC8F,EAAE,EAAE;oBAAEc,KAAK,EAAE;sBAAEU,EAAE,EAAE,MAAM;sBAAEW,EAAE,EAAE;oBAAM;kBAAE,CAAE;kBAAA7C,QAAA,eAC5CxD,OAAA,CAACL,eAAe;oBACd2G,KAAK,EAAC,mDAAyB;oBAC/B9E,KAAK,EAAEtB,QAAQ,CAAC2C,UAAU,IAAI,EAAG;oBACjC1C,QAAQ,EAAEA,CAAA,KAAM,CAAC,CAAE,CAAC;oBAAA;oBACpBoG,QAAQ;oBACRC,QAAQ;kBAAA;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGN9D,OAAA,CAAC5B,GAAG;UAAC8F,EAAE,EAAE;YAAEc,KAAK,EAAE;cAAEU,EAAE,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAM;UAAE,CAAE;UAAAnC,QAAA,eAC5CxD,OAAA,CAACnB,IAAI;YAACuG,OAAO,EAAC,UAAU;YAAClB,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAX,QAAA,eACrCxD,OAAA,CAAClB,WAAW;cAAA0E,QAAA,gBACVxD,OAAA,CAACzB,UAAU;gBAAC6G,OAAO,EAAC,WAAW;gBAAClB,EAAE,EAAE;kBAAEC,EAAE,EAAE,CAAC;kBAAEmB,UAAU,EAAE,MAAM;kBAAEC,OAAO,EAAE,MAAM;kBAAEK,UAAU,EAAE;gBAAS,CAAE;gBAAApC,QAAA,gBACvGxD,OAAA,CAACV,kBAAkB;kBAAC4E,EAAE,EAAE;oBAAE2B,EAAE,EAAE;kBAAE;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,oFAErC,eAAA9D,OAAA,CAAChB,OAAO;kBAACyE,KAAK,EAAC,uMAAsF;kBAAAD,QAAA,eACnGxD,OAAA,CAACjB,UAAU;oBAAC+G,IAAI,EAAC,OAAO;oBAAC5B,EAAE,EAAE;sBAAEiC,EAAE,EAAE;oBAAE,CAAE;oBAAA3C,QAAA,eACrCxD,OAAA,CAACT,eAAe;sBAAC6G,QAAQ,EAAC;oBAAO;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACb9D,OAAA,CAAC3B,SAAS;gBACR4H,SAAS;gBACTK,KAAK,EAAC,8DAA6B;gBACnC/E,IAAI,EAAC,aAAa;gBAClBkF,IAAI,EAAC,MAAM;gBACXjF,KAAK,EAAEtB,QAAQ,CAACyB,WAAW,GAAGzB,QAAQ,CAACyB,WAAW,CAAC+E,cAAc,CAAC,OAAO,CAAC,GAAG,MAAM,GAAG,OAAQ;gBAC9FC,SAAS,EAAE;kBACTC,KAAK,EAAE;oBACLC,QAAQ,EAAE;kBACZ;gBACF,CAAE;gBACF3C,EAAE,EAAE;kBACF,SAAS,EAAE;oBACToB,UAAU,EAAE,MAAM;oBAClBD,KAAK,EAAE1E,KAAK,CAAC8D,OAAO,CAACqC,OAAO,CAAC3B,IAAI;oBACjC4B,eAAe,EAAEpG,KAAK,CAAC8D,OAAO,CAACuC,MAAM,CAACC;kBACxC;gBACF;cAAE;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGN9D,OAAA,CAAC5B,GAAG;UAAC8F,EAAE,EAAE;YAAEc,KAAK,EAAE;UAAO,CAAE;UAAAxB,QAAA,eACzBxD,OAAA,CAACnB,IAAI;YAACuG,OAAO,EAAC,UAAU;YAAClB,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAX,QAAA,eACrCxD,OAAA,CAAClB,WAAW;cAAA0E,QAAA,gBACVxD,OAAA,CAACzB,UAAU;gBAAC6G,OAAO,EAAC,WAAW;gBAAClB,EAAE,EAAE;kBAAEC,EAAE,EAAE,CAAC;kBAAEmB,UAAU,EAAE,MAAM;kBAAEC,OAAO,EAAE,MAAM;kBAAEK,UAAU,EAAE;gBAAS,CAAE;gBAAApC,QAAA,gBACvGxD,OAAA,CAACX,eAAe;kBAAC6E,EAAE,EAAE;oBAAE2B,EAAE,EAAE;kBAAE;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,yCAEpC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb9D,OAAA,CAAC3B,SAAS;gBACR4H,SAAS;gBACTK,KAAK,EAAC,6DAA4B;gBAClC/E,IAAI,EAAC,aAAa;gBAClBC,KAAK,EAAEtB,QAAQ,CAACgH,WAAW,IAAI,EAAG;gBAClC/G,QAAQ,EAAEkB,iBAAkB;gBAC5B8F,SAAS,EAAG7F,CAAC,IAAK;kBAChB;kBACA,IAAIA,CAAC,CAAC8F,GAAG,KAAK,OAAO,IAAI,CAAC9F,CAAC,CAAC+F,QAAQ,EAAE;oBACpC/F,CAAC,CAACgG,eAAe,CAAC,CAAC;kBACrB;gBACF,CAAE;gBACFC,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACRC,WAAW,EAAC;cAAyD;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9D,OAAA,CAACJ,cAAc;QACb8H,IAAI,EAAEjH,kBAAmB;QACzBkH,OAAO,EAAE5F,yBAA0B;QACnC6F,gBAAgB,EAAE5F;MAAqB;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGR9D,OAAA,CAACzB,UAAU;MAAC6G,OAAO,EAAC,IAAI;MAAClB,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAE6B,EAAE,EAAE,CAAC;QAAEV,UAAU,EAAE,MAAM;QAAED,KAAK,EAAE1E,KAAK,CAAC8D,OAAO,CAACS,OAAO,CAACC;MAAK,CAAE;MAAA3B,QAAA,EAAC;IAEtG;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZ,CAAC5D,QAAQ,CAACwC,UAAU,IAAI,EAAE,EAAEmF,GAAG,CAAC,CAACrF,SAAS,EAAED,KAAK,kBAChDvC,OAAA,CAACR,aAAa;MAEZgD,SAAS,EAAEA,SAAU;MACrBrC,QAAQ,EAAG2H,gBAAgB,IAAKxF,qBAAqB,CAACC,KAAK,EAAEuF,gBAAgB,CAAE;MAC/EC,QAAQ,EAAEA,CAAA,KAAM1E,qBAAqB,CAACd,KAAK,CAAE;MAC7CyF,UAAU,EAAE;IAAK,GAJZzF,KAAK;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAKX,CACF,CAAC,eAEF9D,OAAA,CAAC1B,MAAM;MACL8G,OAAO,EAAC,UAAU;MAClBc,SAAS,eAAElG,OAAA,CAACd,OAAO;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACvBiC,OAAO,EAAEjD,kBAAmB;MAC5BoB,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,EACf;IAED;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAGT9D,OAAA,CAACP,yBAAyB;MAACS,QAAQ,EAAEA;IAAS;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEjD9D,OAAA,CAACvB,OAAO;MAACyF,EAAE,EAAE;QAAE+D,EAAE,EAAE;MAAE;IAAE;MAAAtE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE1B9D,OAAA,CAAC5B,GAAG;MAAC8F,EAAE,EAAE;QAAEqB,OAAO,EAAE,MAAM;QAAE2C,cAAc,EAAE,UAAU;QAAElC,EAAE,EAAE;MAAE,CAAE;MAAAxC,QAAA,eAC9DxD,OAAA,CAAC1B,MAAM;QACLmI,IAAI,EAAC,QAAQ;QACbrB,OAAO,EAAC,WAAW;QACnBC,KAAK,EAAC,SAAS;QACfS,IAAI,EAAC,OAAO;QACZU,QAAQ,EAAElG,OAAO,IAAI,CAACJ,QAAQ,CAACgC,UAAU,IAAIhC,QAAQ,CAACgC,UAAU,KAAK,CAAE;QACvE6D,OAAO,EAAEA,CAAA,KAAM;UAAA,IAAAoC,qBAAA;UACbnH,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;YACjDiB,UAAU,EAAEhC,QAAQ,CAACgC,UAAU;YAC/BkG,cAAc,EAAE,OAAOlI,QAAQ,CAACgC,UAAU;YAC1CmG,eAAe,EAAE,EAAAF,qBAAA,GAAAjI,QAAQ,CAACwC,UAAU,cAAAyF,qBAAA,uBAAnBA,qBAAA,CAAqBnE,MAAM,KAAI,CAAC;YACjD1D,OAAO,EAAEA,OAAO;YAChBgI,cAAc,EAAEhI,OAAO,IAAI,CAACJ,QAAQ,CAACgC,UAAU,IAAIhC,QAAQ,CAACgC,UAAU,KAAK;UAC7E,CAAC,CAAC;QACJ,CAAE;QAAAsB,QAAA,EAEDlD,OAAO,GAAG,eAAe,GAAID,MAAM,GAAG,mBAAmB,GAAG;MAAe;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACvD,EAAA,CAxYQN,oBAAoB;EAAA,QAQbhB,QAAQ;AAAA;AAAAsJ,EAAA,GARftI,oBAAoB;AAwY5B;AAED,eAAeA,oBAAoB;AAAC,IAAAsI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}