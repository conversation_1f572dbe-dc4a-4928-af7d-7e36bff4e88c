{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\components\\\\statistics\\\\BarChartDisplay.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Box, Paper, Typography, useTheme } from '@mui/material';\nimport { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';\nimport { Bar } from 'react-chartjs-2';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Register ChartJS components\nChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);\nconst BarChartDisplay = ({\n  data,\n  periodType,\n  title = 'Biểu đồ doanh thu'\n}) => {\n  _s();\n  const theme = useTheme();\n\n  // Get period type label\n  const getPeriodTypeLabel = () => {\n    switch (periodType) {\n      case 'daily':\n        return '<PERSON>anh thu theo ngày';\n      case 'monthly':\n        return '<PERSON>anh thu theo tháng';\n      case 'yearly':\n        return 'Doanh thu theo năm';\n      default:\n        return 'Doanh thu theo thời gian';\n    }\n  };\n\n  // Format currency for tooltip\n  const formatCurrency = value => {\n    return new Intl.NumberFormat('vi-VN', {\n      style: 'currency',\n      currency: 'VND',\n      maximumFractionDigits: 0\n    }).format(value);\n  };\n\n  // Prepare chart data\n  const chartData = {\n    labels: data.map(item => item.label),\n    datasets: [{\n      label: 'Doanh thu',\n      data: data.map(item => item.totalRevenue),\n      backgroundColor: theme.palette.primary.main,\n      borderColor: theme.palette.primary.dark,\n      borderWidth: 1\n    }]\n  };\n\n  // Chart options\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'top'\n      },\n      title: {\n        display: true,\n        text: getPeriodTypeLabel(),\n        font: {\n          size: 16,\n          weight: 'bold'\n        }\n      },\n      tooltip: {\n        callbacks: {\n          label: function (context) {\n            let label = context.dataset.label || '';\n            if (label) {\n              label += ': ';\n            }\n            if (context.parsed.y !== null) {\n              label += formatCurrency(context.parsed.y);\n            }\n            return label;\n          }\n        }\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function (value) {\n            return formatCurrency(value);\n          }\n        }\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    elevation: 2,\n    sx: {\n      p: 2,\n      mb: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        height: 400,\n        position: 'relative'\n      },\n      children: data.length > 0 ? /*#__PURE__*/_jsxDEV(Bar, {\n        data: chartData,\n        options: chartOptions\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          height: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          color: \"text.secondary\",\n          children: \"Kh\\xF4ng c\\xF3 d\\u1EEF li\\u1EC7u \\u0111\\u1EC3 hi\\u1EC3n th\\u1ECB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 5\n  }, this);\n};\n_s(BarChartDisplay, \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function () {\n  return [useTheme];\n});\n_c = BarChartDisplay;\nexport default BarChartDisplay;\nvar _c;\n$RefreshReg$(_c, \"BarChartDisplay\");", "map": {"version": 3, "names": ["React", "Box", "Paper", "Typography", "useTheme", "Chart", "ChartJS", "CategoryScale", "LinearScale", "BarElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "Bar", "jsxDEV", "_jsxDEV", "register", "BarChartDisplay", "data", "periodType", "title", "_s", "theme", "getPeriodTypeLabel", "formatCurrency", "value", "Intl", "NumberFormat", "style", "currency", "maximumFractionDigits", "format", "chartData", "labels", "map", "item", "label", "datasets", "totalRevenue", "backgroundColor", "palette", "primary", "main", "borderColor", "dark", "borderWidth", "chartOptions", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "display", "text", "font", "size", "weight", "tooltip", "callbacks", "context", "dataset", "parsed", "y", "scales", "beginAtZero", "ticks", "callback", "elevation", "sx", "p", "mb", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "height", "length", "options", "justifyContent", "alignItems", "color", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/statistics/BarChartDisplay.tsx"], "sourcesContent": ["import React from 'react';\nimport { Box, Paper, Typography, useTheme } from '@mui/material';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend\n} from 'chart.js';\nimport { Bar } from 'react-chartjs-2';\nimport { TimeBasedRevenue } from '../../models';\n\n// Register ChartJS components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\ninterface BarChartDisplayProps {\n  data: TimeBasedRevenue[];\n  periodType: string;\n  title?: string;\n}\n\nconst BarChartDisplay: React.FC<BarChartDisplayProps> = ({\n  data,\n  periodType,\n  title = 'Biểu đồ doanh thu'\n}) => {\n  const theme = useTheme();\n\n  // Get period type label\n  const getPeriodTypeLabel = (): string => {\n    switch (periodType) {\n      case 'daily':\n        return 'Doanh thu theo ngày';\n      case 'monthly':\n        return 'Doanh thu theo tháng';\n      case 'yearly':\n        return '<PERSON>anh thu theo năm';\n      default:\n        return '<PERSON>anh thu theo thời gian';\n    }\n  };\n\n  // Format currency for tooltip\n  const formatCurrency = (value: number): string => {\n    return new Intl.NumberFormat('vi-VN', {\n      style: 'currency',\n      currency: 'VND',\n      maximumFractionDigits: 0\n    }).format(value);\n  };\n\n  // Prepare chart data\n  const chartData = {\n    labels: data.map(item => item.label),\n    datasets: [\n      {\n        label: 'Doanh thu',\n        data: data.map(item => item.totalRevenue),\n        backgroundColor: theme.palette.primary.main,\n        borderColor: theme.palette.primary.dark,\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  // Chart options\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'top' as const,\n      },\n      title: {\n        display: true,\n        text: getPeriodTypeLabel(),\n        font: {\n          size: 16,\n          weight: 'bold' as const,\n        },\n      },\n      tooltip: {\n        callbacks: {\n          label: function(context: any) {\n            let label = context.dataset.label || '';\n            if (label) {\n              label += ': ';\n            }\n            if (context.parsed.y !== null) {\n              label += formatCurrency(context.parsed.y);\n            }\n            return label;\n          }\n        }\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function(value: any) {\n            return formatCurrency(value);\n          }\n        }\n      }\n    },\n  };\n\n  return (\n    <Paper elevation={2} sx={{ p: 2, mb: 3 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        {title}\n      </Typography>\n      <Box sx={{ height: 400, position: 'relative' }}>\n        {data.length > 0 ? (\n          <Bar data={chartData} options={chartOptions} />\n        ) : (\n          <Box\n            sx={{\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              height: '100%'\n            }}\n          >\n            <Typography variant=\"subtitle1\" color=\"text.secondary\">\n              Không có dữ liệu để hiển thị\n            </Typography>\n          </Box>\n        )}\n      </Box>\n    </Paper>\n  );\n};\n\nexport default BarChartDisplay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,KAAK,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AAChE,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,QACD,UAAU;AACjB,SAASC,GAAG,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGtC;AACAT,OAAO,CAACU,QAAQ,CACdT,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MACF,CAAC;AAQD,MAAMK,eAA+C,GAAGA,CAAC;EACvDC,IAAI;EACJC,UAAU;EACVC,KAAK,GAAG;AACV,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,KAAK,GAAGlB,QAAQ,CAAC,CAAC;;EAExB;EACA,MAAMmB,kBAAkB,GAAGA,CAAA,KAAc;IACvC,QAAQJ,UAAU;MAChB,KAAK,OAAO;QACV,OAAO,qBAAqB;MAC9B,KAAK,SAAS;QACZ,OAAO,sBAAsB;MAC/B,KAAK,QAAQ;QACX,OAAO,oBAAoB;MAC7B;QACE,OAAO,0BAA0B;IACrC;EACF,CAAC;;EAED;EACA,MAAMK,cAAc,GAAIC,KAAa,IAAa;IAChD,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACN,KAAK,CAAC;EAClB,CAAC;;EAED;EACA,MAAMO,SAAS,GAAG;IAChBC,MAAM,EAAEf,IAAI,CAACgB,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC;IACpCC,QAAQ,EAAE,CACR;MACED,KAAK,EAAE,WAAW;MAClBlB,IAAI,EAAEA,IAAI,CAACgB,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACG,YAAY,CAAC;MACzCC,eAAe,EAAEjB,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAAI;MAC3CC,WAAW,EAAErB,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACG,IAAI;MACvCC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,mBAAmB,EAAE,KAAK;IAC1BC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE;MACZ,CAAC;MACD/B,KAAK,EAAE;QACLgC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE9B,kBAAkB,CAAC,CAAC;QAC1B+B,IAAI,EAAE;UACJC,IAAI,EAAE,EAAE;UACRC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,SAAS,EAAE;UACTtB,KAAK,EAAE,SAAAA,CAASuB,OAAY,EAAE;YAC5B,IAAIvB,KAAK,GAAGuB,OAAO,CAACC,OAAO,CAACxB,KAAK,IAAI,EAAE;YACvC,IAAIA,KAAK,EAAE;cACTA,KAAK,IAAI,IAAI;YACf;YACA,IAAIuB,OAAO,CAACE,MAAM,CAACC,CAAC,KAAK,IAAI,EAAE;cAC7B1B,KAAK,IAAIZ,cAAc,CAACmC,OAAO,CAACE,MAAM,CAACC,CAAC,CAAC;YAC3C;YACA,OAAO1B,KAAK;UACd;QACF;MACF;IACF,CAAC;IACD2B,MAAM,EAAE;MACND,CAAC,EAAE;QACDE,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;UACLC,QAAQ,EAAE,SAAAA,CAASzC,KAAU,EAAE;YAC7B,OAAOD,cAAc,CAACC,KAAK,CAAC;UAC9B;QACF;MACF;IACF;EACF,CAAC;EAED,oBACEV,OAAA,CAACb,KAAK;IAACiE,SAAS,EAAE,CAAE;IAACC,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACvCxD,OAAA,CAACZ,UAAU;MAACqE,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAClCnD;IAAK;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eACb9D,OAAA,CAACd,GAAG;MAACmE,EAAE,EAAE;QAAEU,MAAM,EAAE,GAAG;QAAE3B,QAAQ,EAAE;MAAW,CAAE;MAAAoB,QAAA,EAC5CrD,IAAI,CAAC6D,MAAM,GAAG,CAAC,gBACdhE,OAAA,CAACF,GAAG;QAACK,IAAI,EAAEc,SAAU;QAACgD,OAAO,EAAElC;MAAa;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAE/C9D,OAAA,CAACd,GAAG;QACFmE,EAAE,EAAE;UACFhB,OAAO,EAAE,MAAM;UACf6B,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE,QAAQ;UACpBJ,MAAM,EAAE;QACV,CAAE;QAAAP,QAAA,eAEFxD,OAAA,CAACZ,UAAU;UAACqE,OAAO,EAAC,WAAW;UAACW,KAAK,EAAC,gBAAgB;UAAAZ,QAAA,EAAC;QAEvD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAACxD,EAAA,CAhHIJ,eAA+C;EAAA,QAKrCb,QAAQ;AAAA;AAAAgF,EAAA,GALlBnE,eAA+C;AAkHrD,eAAeA,eAAe;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}