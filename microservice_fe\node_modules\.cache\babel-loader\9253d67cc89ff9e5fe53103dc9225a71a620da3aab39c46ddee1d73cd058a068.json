{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\components\\\\payment\\\\PaymentForm.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Button, Dialog, DialogActions, DialogContent, DialogTitle, TextField, FormControl, InputLabel, Select, MenuItem, Typography, Paper, Divider, Alert, IconButton } from '@mui/material';\nimport CloseIcon from '@mui/icons-material/Close';\nimport PaymentIcon from '@mui/icons-material/Payment';\nimport ReceiptIcon from '@mui/icons-material/Receipt';\nimport InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';\nimport { PaymentMethodMap } from '../../models';\nimport { formatCurrency } from '../../utils/formatters';\nimport { formatDateLocalized } from '../../utils/dateUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PaymentForm = ({\n  open,\n  contract,\n  onClose,\n  onSubmit,\n  remainingAmount,\n  loading = false\n}) => {\n  _s();\n  const [paymentAmount, setPaymentAmount] = useState(0);\n  const [paymentMethod, setPaymentMethod] = useState(0); // Default: Tiền mặt\n  const [note, setNote] = useState('');\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    if (open && contract) {\n      // Reset form when dialog opens\n      setPaymentAmount(remainingAmount);\n      setPaymentMethod(0);\n      setNote('');\n      setError(null);\n    }\n  }, [open, contract, remainingAmount]);\n  const handleSubmit = e => {\n    // Prevent default form submission behavior\n    if (e) {\n      e.preventDefault();\n    }\n    if (!contract || loading) return;\n\n    // Clear previous errors\n    setError(null);\n\n    // Validate payment amount\n    if (!paymentAmount || paymentAmount <= 0) {\n      setError('Số tiền thanh toán phải lớn hơn 0');\n      return;\n    }\n    if (paymentAmount > remainingAmount) {\n      setError(`Số tiền thanh toán không được vượt quá số tiền còn lại (${formatCurrency(remainingAmount)})`);\n      return;\n    }\n\n    // Validate contract data\n    if (!contract.id) {\n      setError('Thông tin hợp đồng không hợp lệ');\n      return;\n    }\n    if (!contract.customerId) {\n      setError('Thông tin khách hàng không hợp lệ');\n      return;\n    }\n    console.log('🚀 Preparing payment submission:', {\n      contractId: contract.id,\n      customerId: contract.customerId,\n      amount: paymentAmount,\n      method: paymentMethod,\n      remainingAmount\n    });\n\n    // Create payment object\n    const payment = {\n      paymentDate: new Date().toISOString(),\n      paymentMethod,\n      paymentAmount,\n      note: note || undefined,\n      customerContractId: contract.id,\n      customerId: contract.customerId\n    };\n    onSubmit(payment);\n  };\n  const formatDate = dateString => {\n    if (!dateString) return '-';\n    return formatDateLocalized(dateString);\n  };\n  if (!contract) return null;\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    sx: {\n      '& .MuiDialog-paper': {\n        borderRadius: 2,\n        boxShadow: 24\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      sx: {\n        bgcolor: 'primary.main',\n        color: 'primary.contrastText',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        px: 3,\n        py: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(PaymentIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: \"Thanh to\\xE1n h\\u1EE3p \\u0111\\u1ED3ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n        edge: \"end\",\n        color: \"inherit\",\n        onClick: onClose,\n        \"aria-label\": \"close\",\n        children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Paper, {\n        variant: \"outlined\",\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          fontWeight: \"bold\",\n          gutterBottom: true,\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(InfoOutlinedIcon, {\n            fontSize: \"small\",\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), \"Th\\xF4ng tin h\\u1EE3p \\u0111\\u1ED3ng\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'grid',\n            gridTemplateColumns: {\n              xs: '1fr',\n              sm: '1fr 1fr'\n            },\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"M\\xE3 h\\u1EE3p \\u0111\\u1ED3ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              fontWeight: \"medium\",\n              children: [\"#\", contract.id]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Kh\\xE1ch h\\xE0ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              fontWeight: \"medium\",\n              children: contract.customerName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Ng\\xE0y b\\u1EAFt \\u0111\\u1EA7u\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: formatDate(contract.startingDate)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Ng\\xE0y k\\u1EBFt th\\xFAc\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: formatDate(contract.endingDate)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'grid',\n            gridTemplateColumns: {\n              xs: '1fr',\n              sm: '1fr 1fr 1fr'\n            },\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Paper, {\n            variant: \"outlined\",\n            sx: {\n              p: 1.5,\n              bgcolor: 'background.default',\n              borderColor: 'divider'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"T\\u1ED5ng gi\\xE1 tr\\u1ECB h\\u1EE3p \\u0111\\u1ED3ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: formatCurrency(contract.totalAmount)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paper, {\n            variant: \"outlined\",\n            sx: {\n              p: 1.5,\n              bgcolor: 'background.default',\n              borderColor: 'divider'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"\\u0110\\xE3 thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: formatCurrency(contract.totalPaid || 0)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 1.5,\n              bgcolor: 'primary.light',\n              color: 'primary.contrastText',\n              borderRadius: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"inherit\",\n              children: \"C\\xF2n l\\u1EA1i c\\u1EA7n thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              children: formatCurrency(remainingAmount)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        variant: \"outlined\",\n        sx: {\n          p: 2,\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          fontWeight: \"bold\",\n          gutterBottom: true,\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(ReceiptIcon, {\n            fontSize: \"small\",\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), \"Th\\xF4ng tin thanh to\\xE1n\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          component: \"form\",\n          id: \"payment-form\",\n          onSubmit: handleSubmit,\n          sx: {\n            display: 'grid',\n            gridTemplateColumns: {\n              xs: '1fr',\n              sm: '1fr 1fr'\n            },\n            gap: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"S\\u1ED1 ti\\u1EC1n thanh to\\xE1n\",\n            type: \"number\",\n            fullWidth: true,\n            value: paymentAmount,\n            onChange: e => setPaymentAmount(Number(e.target.value)),\n            onKeyDown: e => {\n              if (e.key === 'Enter') {\n                e.preventDefault();\n                handleSubmit();\n              }\n            },\n            sx: {\n              '& .MuiInputBase-input': {\n                paddingRight: '40px'\n              },\n              '& .MuiInputBase-root': {\n                position: 'relative',\n                '&::after': {\n                  content: '\"VND\"',\n                  position: 'absolute',\n                  right: '14px',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  color: 'rgba(0, 0, 0, 0.54)',\n                  pointerEvents: 'none'\n                }\n              }\n            },\n            error: !!error,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Ph\\u01B0\\u01A1ng th\\u1EE9c thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: paymentMethod,\n              label: \"Ph\\u01B0\\u01A1ng th\\u1EE9c thanh to\\xE1n\",\n              onChange: e => setPaymentMethod(Number(e.target.value)),\n              children: Object.entries(PaymentMethodMap).map(([value, label]) => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: value,\n                children: label\n              }, value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              gridColumn: {\n                xs: '1',\n                sm: '1 / span 2'\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              label: \"Ghi ch\\xFA\",\n              fullWidth: true,\n              multiline: true,\n              rows: 3,\n              value: note,\n              onChange: e => setNote(e.target.value),\n              placeholder: \"Nh\\u1EADp ghi ch\\xFA v\\u1EC1 thanh to\\xE1n (n\\u1EBFu c\\xF3)\",\n              onKeyDown: e => {\n                // Prevent Enter from submitting in multiline text field\n                if (e.key === 'Enter' && !e.shiftKey) {\n                  e.stopPropagation();\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      sx: {\n        px: 3,\n        py: 2,\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        color: \"inherit\",\n        variant: \"outlined\",\n        startIcon: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 22\n        }, this),\n        children: \"H\\u1EE7y\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"submit\",\n        form: \"payment-form\",\n        variant: \"contained\",\n        color: \"primary\",\n        startIcon: /*#__PURE__*/_jsxDEV(PaymentIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 22\n        }, this),\n        size: \"large\",\n        disabled: loading || !paymentAmount || paymentAmount <= 0,\n        children: loading ? 'Đang xử lý...' : 'Xác nhận thanh toán'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 127,\n    columnNumber: 5\n  }, this);\n};\n_s(PaymentForm, \"740mACJP7SK8ge809rHBwJUfQe4=\");\n_c = PaymentForm;\nexport default PaymentForm;\nvar _c;\n$RefreshReg$(_c, \"PaymentForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "<PERSON><PERSON>", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogTitle", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Typography", "Paper", "Divider", "<PERSON><PERSON>", "IconButton", "CloseIcon", "PaymentIcon", "ReceiptIcon", "InfoOutlinedIcon", "PaymentMethodMap", "formatCurrency", "formatDateLocalized", "jsxDEV", "_jsxDEV", "PaymentForm", "open", "contract", "onClose", "onSubmit", "remainingAmount", "loading", "_s", "paymentAmount", "setPaymentAmount", "paymentMethod", "setPaymentMethod", "note", "setNote", "error", "setError", "handleSubmit", "e", "preventDefault", "id", "customerId", "console", "log", "contractId", "amount", "method", "payment", "paymentDate", "Date", "toISOString", "undefined", "customerContractId", "formatDate", "dateString", "max<PERSON><PERSON><PERSON>", "fullWidth", "sx", "borderRadius", "boxShadow", "children", "bgcolor", "color", "display", "justifyContent", "alignItems", "px", "py", "gap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "edge", "onClick", "p", "mb", "fontWeight", "gutterBottom", "fontSize", "gridTemplateColumns", "xs", "sm", "customerName", "startingDate", "endingDate", "my", "borderColor", "totalAmount", "totalPaid", "severity", "component", "label", "type", "value", "onChange", "Number", "target", "onKeyDown", "key", "paddingRight", "position", "content", "right", "top", "transform", "pointerEvents", "required", "Object", "entries", "map", "gridColumn", "multiline", "rows", "placeholder", "shift<PERSON>ey", "stopPropagation", "startIcon", "form", "size", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/payment/PaymentForm.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Button,\n  Dialog,\n  <PERSON>alogActions,\n  DialogContent,\n  DialogTitle,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Typography,\n  Paper,\n  Divider,\n  Alert,\n  IconButton,\n} from '@mui/material';\n\nimport CloseIcon from '@mui/icons-material/Close';\nimport PaymentIcon from '@mui/icons-material/Payment';\nimport ReceiptIcon from '@mui/icons-material/Receipt';\nimport InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';\nimport { CustomerContract, CustomerPayment, PaymentMethodMap } from '../../models';\nimport { formatCurrency } from '../../utils/formatters';\nimport { formatDateLocalized } from '../../utils/dateUtils';\n\ninterface PaymentFormProps {\n  open: boolean;\n  contract: CustomerContract | null;\n  onClose: () => void;\n  onSubmit: (payment: CustomerPayment) => void;\n  remainingAmount: number;\n  loading?: boolean;\n}\n\nconst PaymentForm: React.FC<PaymentFormProps> = ({\n  open,\n  contract,\n  onClose,\n  onSubmit,\n  remainingAmount,\n  loading = false,\n}) => {\n\n\n  const [paymentAmount, setPaymentAmount] = useState<number>(0);\n  const [paymentMethod, setPaymentMethod] = useState<number>(0); // Default: Tiền mặt\n  const [note, setNote] = useState<string>('');\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (open && contract) {\n      // Reset form when dialog opens\n      setPaymentAmount(remainingAmount);\n      setPaymentMethod(0);\n      setNote('');\n      setError(null);\n    }\n  }, [open, contract, remainingAmount]);\n\n  const handleSubmit = (e?: React.FormEvent) => {\n    // Prevent default form submission behavior\n    if (e) {\n      e.preventDefault();\n    }\n\n    if (!contract || loading) return;\n\n    // Clear previous errors\n    setError(null);\n\n    // Validate payment amount\n    if (!paymentAmount || paymentAmount <= 0) {\n      setError('Số tiền thanh toán phải lớn hơn 0');\n      return;\n    }\n\n    if (paymentAmount > remainingAmount) {\n      setError(`Số tiền thanh toán không được vượt quá số tiền còn lại (${formatCurrency(remainingAmount)})`);\n      return;\n    }\n\n    // Validate contract data\n    if (!contract.id) {\n      setError('Thông tin hợp đồng không hợp lệ');\n      return;\n    }\n\n    if (!contract.customerId) {\n      setError('Thông tin khách hàng không hợp lệ');\n      return;\n    }\n\n    console.log('🚀 Preparing payment submission:', {\n      contractId: contract.id,\n      customerId: contract.customerId,\n      amount: paymentAmount,\n      method: paymentMethod,\n      remainingAmount\n    });\n\n    // Create payment object\n    const payment: CustomerPayment = {\n      paymentDate: new Date().toISOString(),\n      paymentMethod,\n      paymentAmount,\n      note: note || undefined,\n      customerContractId: contract.id!,\n      customerId: contract.customerId,\n    };\n\n    onSubmit(payment);\n  };\n\n  const formatDate = (dateString?: string) => {\n    if (!dateString) return '-';\n    return formatDateLocalized(dateString);\n  };\n\n\n\n  if (!contract) return null;\n\n  return (\n    <Dialog\n      open={open}\n      onClose={onClose}\n      maxWidth=\"md\"\n      fullWidth\n      sx={{\n        '& .MuiDialog-paper': {\n          borderRadius: 2,\n          boxShadow: 24,\n        }\n      }}\n    >\n      <DialogTitle sx={{\n        bgcolor: 'primary.main',\n        color: 'primary.contrastText',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        px: 3,\n        py: 2\n      }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <PaymentIcon />\n          <Typography variant=\"h6\">Thanh toán hợp đồng</Typography>\n        </Box>\n        <IconButton\n          edge=\"end\"\n          color=\"inherit\"\n          onClick={onClose}\n          aria-label=\"close\"\n        >\n          <CloseIcon />\n        </IconButton>\n      </DialogTitle>\n\n      <DialogContent sx={{ p: 3 }}>\n        <Paper variant=\"outlined\" sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"subtitle1\" fontWeight=\"bold\" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <InfoOutlinedIcon fontSize=\"small\" color=\"primary\" />\n            Thông tin hợp đồng\n          </Typography>\n\n          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' }, gap: 2 }}>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Mã hợp đồng\n              </Typography>\n              <Typography variant=\"body1\" fontWeight=\"medium\">\n                #{contract.id}\n              </Typography>\n            </Box>\n\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Khách hàng\n              </Typography>\n              <Typography variant=\"body1\" fontWeight=\"medium\">\n                {contract.customerName}\n              </Typography>\n            </Box>\n\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Ngày bắt đầu\n              </Typography>\n              <Typography variant=\"body1\">\n                {formatDate(contract.startingDate)}\n              </Typography>\n            </Box>\n\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Ngày kết thúc\n              </Typography>\n              <Typography variant=\"body1\">\n                {formatDate(contract.endingDate)}\n              </Typography>\n            </Box>\n\n\n          </Box>\n\n          <Divider sx={{ my: 2 }} />\n\n          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr 1fr' }, gap: 2 }}>\n            <Paper\n              variant=\"outlined\"\n              sx={{\n                p: 1.5,\n                bgcolor: 'background.default',\n                borderColor: 'divider'\n              }}\n            >\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Tổng giá trị hợp đồng\n              </Typography>\n              <Typography variant=\"h6\">\n                {formatCurrency(contract.totalAmount)}\n              </Typography>\n            </Paper>\n\n            <Paper\n              variant=\"outlined\"\n              sx={{\n                p: 1.5,\n                bgcolor: 'background.default',\n                borderColor: 'divider'\n              }}\n            >\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Đã thanh toán\n              </Typography>\n              <Typography variant=\"h6\">\n                {formatCurrency(contract.totalPaid || 0)}\n              </Typography>\n            </Paper>\n\n            <Paper\n              sx={{\n                p: 1.5,\n                bgcolor: 'primary.light',\n                color: 'primary.contrastText',\n                borderRadius: 1\n              }}\n            >\n              <Typography variant=\"body2\" color=\"inherit\">\n                Còn lại cần thanh toán\n              </Typography>\n              <Typography variant=\"h6\" fontWeight=\"bold\">\n                {formatCurrency(remainingAmount)}\n              </Typography>\n            </Paper>\n          </Box>\n        </Paper>\n\n        <Paper variant=\"outlined\" sx={{ p: 2, mb: 2 }}>\n          <Typography variant=\"subtitle1\" fontWeight=\"bold\" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <ReceiptIcon fontSize=\"small\" color=\"primary\" />\n            Thông tin thanh toán\n          </Typography>\n\n          {error && (\n            <Alert severity=\"error\" sx={{ mb: 2 }}>\n              {error}\n            </Alert>\n          )}\n\n          <Box\n            component=\"form\"\n            id=\"payment-form\"\n            onSubmit={handleSubmit}\n            sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' }, gap: 3 }}\n          >\n            <TextField\n              label=\"Số tiền thanh toán\"\n              type=\"number\"\n              fullWidth\n              value={paymentAmount}\n              onChange={(e) => setPaymentAmount(Number(e.target.value))}\n              onKeyDown={(e) => {\n                if (e.key === 'Enter') {\n                  e.preventDefault();\n                  handleSubmit();\n                }\n              }}\n              sx={{\n                '& .MuiInputBase-input': { paddingRight: '40px' },\n                '& .MuiInputBase-root': {\n                  position: 'relative',\n                  '&::after': {\n                    content: '\"VND\"',\n                    position: 'absolute',\n                    right: '14px',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: 'rgba(0, 0, 0, 0.54)',\n                    pointerEvents: 'none'\n                  }\n                }\n              }}\n              error={!!error}\n              required\n            />\n\n            <FormControl fullWidth required>\n              <InputLabel>Phương thức thanh toán</InputLabel>\n              <Select\n                value={paymentMethod}\n                label=\"Phương thức thanh toán\"\n                onChange={(e) => setPaymentMethod(Number(e.target.value))}\n              >\n                {Object.entries(PaymentMethodMap).map(([value, label]) => (\n                  <MenuItem key={value} value={value}>\n                    {label}\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n\n            <Box sx={{ gridColumn: { xs: '1', sm: '1 / span 2' } }}>\n              <TextField\n                label=\"Ghi chú\"\n                fullWidth\n                multiline\n                rows={3}\n                value={note}\n                onChange={(e) => setNote(e.target.value)}\n                placeholder=\"Nhập ghi chú về thanh toán (nếu có)\"\n                onKeyDown={(e) => {\n                  // Prevent Enter from submitting in multiline text field\n                  if (e.key === 'Enter' && !e.shiftKey) {\n                    e.stopPropagation();\n                  }\n                }}\n              />\n            </Box>\n          </Box>\n        </Paper>\n      </DialogContent>\n\n      <DialogActions sx={{ px: 3, py: 2, justifyContent: 'space-between' }}>\n        <Button\n          onClick={onClose}\n          color=\"inherit\"\n          variant=\"outlined\"\n          startIcon={<CloseIcon />}\n        >\n          Hủy\n        </Button>\n        <Button\n          type=\"submit\"\n          form=\"payment-form\"\n          variant=\"contained\"\n          color=\"primary\"\n          startIcon={<PaymentIcon />}\n          size=\"large\"\n          disabled={loading || !paymentAmount || paymentAmount <= 0}\n        >\n          {loading ? 'Đang xử lý...' : 'Xác nhận thanh toán'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default PaymentForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,KAAK,EACLC,UAAU,QACL,eAAe;AAEtB,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,SAA4CC,gBAAgB,QAAQ,cAAc;AAClF,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,mBAAmB,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAW5D,MAAMC,WAAuC,GAAGA,CAAC;EAC/CC,IAAI;EACJC,QAAQ;EACRC,OAAO;EACPC,QAAQ;EACRC,eAAe;EACfC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EAGJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAS,CAAC,CAAC;EAC7D,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAS,CAAC,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACuC,IAAI,EAAEC,OAAO,CAAC,GAAGxC,QAAQ,CAAS,EAAE,CAAC;EAC5C,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,IAAI2B,IAAI,IAAIC,QAAQ,EAAE;MACpB;MACAO,gBAAgB,CAACJ,eAAe,CAAC;MACjCM,gBAAgB,CAAC,CAAC,CAAC;MACnBE,OAAO,CAAC,EAAE,CAAC;MACXE,QAAQ,CAAC,IAAI,CAAC;IAChB;EACF,CAAC,EAAE,CAACd,IAAI,EAAEC,QAAQ,EAAEG,eAAe,CAAC,CAAC;EAErC,MAAMW,YAAY,GAAIC,CAAmB,IAAK;IAC5C;IACA,IAAIA,CAAC,EAAE;MACLA,CAAC,CAACC,cAAc,CAAC,CAAC;IACpB;IAEA,IAAI,CAAChB,QAAQ,IAAII,OAAO,EAAE;;IAE1B;IACAS,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,IAAI,CAACP,aAAa,IAAIA,aAAa,IAAI,CAAC,EAAE;MACxCO,QAAQ,CAAC,mCAAmC,CAAC;MAC7C;IACF;IAEA,IAAIP,aAAa,GAAGH,eAAe,EAAE;MACnCU,QAAQ,CAAC,2DAA2DnB,cAAc,CAACS,eAAe,CAAC,GAAG,CAAC;MACvG;IACF;;IAEA;IACA,IAAI,CAACH,QAAQ,CAACiB,EAAE,EAAE;MAChBJ,QAAQ,CAAC,iCAAiC,CAAC;MAC3C;IACF;IAEA,IAAI,CAACb,QAAQ,CAACkB,UAAU,EAAE;MACxBL,QAAQ,CAAC,mCAAmC,CAAC;MAC7C;IACF;IAEAM,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;MAC9CC,UAAU,EAAErB,QAAQ,CAACiB,EAAE;MACvBC,UAAU,EAAElB,QAAQ,CAACkB,UAAU;MAC/BI,MAAM,EAAEhB,aAAa;MACrBiB,MAAM,EAAEf,aAAa;MACrBL;IACF,CAAC,CAAC;;IAEF;IACA,MAAMqB,OAAwB,GAAG;MAC/BC,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACrCnB,aAAa;MACbF,aAAa;MACbI,IAAI,EAAEA,IAAI,IAAIkB,SAAS;MACvBC,kBAAkB,EAAE7B,QAAQ,CAACiB,EAAG;MAChCC,UAAU,EAAElB,QAAQ,CAACkB;IACvB,CAAC;IAEDhB,QAAQ,CAACsB,OAAO,CAAC;EACnB,CAAC;EAED,MAAMM,UAAU,GAAIC,UAAmB,IAAK;IAC1C,IAAI,CAACA,UAAU,EAAE,OAAO,GAAG;IAC3B,OAAOpC,mBAAmB,CAACoC,UAAU,CAAC;EACxC,CAAC;EAID,IAAI,CAAC/B,QAAQ,EAAE,OAAO,IAAI;EAE1B,oBACEH,OAAA,CAACtB,MAAM;IACLwB,IAAI,EAAEA,IAAK;IACXE,OAAO,EAAEA,OAAQ;IACjB+B,QAAQ,EAAC,IAAI;IACbC,SAAS;IACTC,EAAE,EAAE;MACF,oBAAoB,EAAE;QACpBC,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE;MACb;IACF,CAAE;IAAAC,QAAA,gBAEFxC,OAAA,CAACnB,WAAW;MAACwD,EAAE,EAAE;QACfI,OAAO,EAAE,cAAc;QACvBC,KAAK,EAAE,sBAAsB;QAC7BC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE;MACN,CAAE;MAAAP,QAAA,gBACAxC,OAAA,CAACxB,GAAG;QAAC6D,EAAE,EAAE;UAAEM,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEG,GAAG,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACzDxC,OAAA,CAACP,WAAW;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACfpD,OAAA,CAACb,UAAU;UAACkE,OAAO,EAAC,IAAI;UAAAb,QAAA,EAAC;QAAmB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eACNpD,OAAA,CAACT,UAAU;QACT+D,IAAI,EAAC,KAAK;QACVZ,KAAK,EAAC,SAAS;QACfa,OAAO,EAAEnD,OAAQ;QACjB,cAAW,OAAO;QAAAoC,QAAA,eAElBxC,OAAA,CAACR,SAAS;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEdpD,OAAA,CAACpB,aAAa;MAACyD,EAAE,EAAE;QAAEmB,CAAC,EAAE;MAAE,CAAE;MAAAhB,QAAA,gBAC1BxC,OAAA,CAACZ,KAAK;QAACiE,OAAO,EAAC,UAAU;QAAChB,EAAE,EAAE;UAAEmB,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAjB,QAAA,gBAC5CxC,OAAA,CAACb,UAAU;UAACkE,OAAO,EAAC,WAAW;UAACK,UAAU,EAAC,MAAM;UAACC,YAAY;UAACtB,EAAE,EAAE;YAAEM,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEG,GAAG,EAAE;UAAE,CAAE;UAAAR,QAAA,gBACnHxC,OAAA,CAACL,gBAAgB;YAACiE,QAAQ,EAAC,OAAO;YAAClB,KAAK,EAAC;UAAS;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,wCAEvD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbpD,OAAA,CAACxB,GAAG;UAAC6D,EAAE,EAAE;YAAEM,OAAO,EAAE,MAAM;YAAEkB,mBAAmB,EAAE;cAAEC,EAAE,EAAE,KAAK;cAAEC,EAAE,EAAE;YAAU,CAAC;YAAEf,GAAG,EAAE;UAAE,CAAE;UAAAR,QAAA,gBACtFxC,OAAA,CAACxB,GAAG;YAAAgE,QAAA,gBACFxC,OAAA,CAACb,UAAU;cAACkE,OAAO,EAAC,OAAO;cAACX,KAAK,EAAC,gBAAgB;cAAAF,QAAA,EAAC;YAEnD;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpD,OAAA,CAACb,UAAU;cAACkE,OAAO,EAAC,OAAO;cAACK,UAAU,EAAC,QAAQ;cAAAlB,QAAA,GAAC,GAC7C,EAACrC,QAAQ,CAACiB,EAAE;YAAA;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENpD,OAAA,CAACxB,GAAG;YAAAgE,QAAA,gBACFxC,OAAA,CAACb,UAAU;cAACkE,OAAO,EAAC,OAAO;cAACX,KAAK,EAAC,gBAAgB;cAAAF,QAAA,EAAC;YAEnD;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpD,OAAA,CAACb,UAAU;cAACkE,OAAO,EAAC,OAAO;cAACK,UAAU,EAAC,QAAQ;cAAAlB,QAAA,EAC5CrC,QAAQ,CAAC6D;YAAY;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENpD,OAAA,CAACxB,GAAG;YAAAgE,QAAA,gBACFxC,OAAA,CAACb,UAAU;cAACkE,OAAO,EAAC,OAAO;cAACX,KAAK,EAAC,gBAAgB;cAAAF,QAAA,EAAC;YAEnD;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpD,OAAA,CAACb,UAAU;cAACkE,OAAO,EAAC,OAAO;cAAAb,QAAA,EACxBP,UAAU,CAAC9B,QAAQ,CAAC8D,YAAY;YAAC;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENpD,OAAA,CAACxB,GAAG;YAAAgE,QAAA,gBACFxC,OAAA,CAACb,UAAU;cAACkE,OAAO,EAAC,OAAO;cAACX,KAAK,EAAC,gBAAgB;cAAAF,QAAA,EAAC;YAEnD;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpD,OAAA,CAACb,UAAU;cAACkE,OAAO,EAAC,OAAO;cAAAb,QAAA,EACxBP,UAAU,CAAC9B,QAAQ,CAAC+D,UAAU;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGH,CAAC,eAENpD,OAAA,CAACX,OAAO;UAACgD,EAAE,EAAE;YAAE8B,EAAE,EAAE;UAAE;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE1BpD,OAAA,CAACxB,GAAG;UAAC6D,EAAE,EAAE;YAAEM,OAAO,EAAE,MAAM;YAAEkB,mBAAmB,EAAE;cAAEC,EAAE,EAAE,KAAK;cAAEC,EAAE,EAAE;YAAc,CAAC;YAAEf,GAAG,EAAE;UAAE,CAAE;UAAAR,QAAA,gBAC1FxC,OAAA,CAACZ,KAAK;YACJiE,OAAO,EAAC,UAAU;YAClBhB,EAAE,EAAE;cACFmB,CAAC,EAAE,GAAG;cACNf,OAAO,EAAE,oBAAoB;cAC7B2B,WAAW,EAAE;YACf,CAAE;YAAA5B,QAAA,gBAEFxC,OAAA,CAACb,UAAU;cAACkE,OAAO,EAAC,OAAO;cAACX,KAAK,EAAC,gBAAgB;cAAAF,QAAA,EAAC;YAEnD;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpD,OAAA,CAACb,UAAU;cAACkE,OAAO,EAAC,IAAI;cAAAb,QAAA,EACrB3C,cAAc,CAACM,QAAQ,CAACkE,WAAW;YAAC;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAERpD,OAAA,CAACZ,KAAK;YACJiE,OAAO,EAAC,UAAU;YAClBhB,EAAE,EAAE;cACFmB,CAAC,EAAE,GAAG;cACNf,OAAO,EAAE,oBAAoB;cAC7B2B,WAAW,EAAE;YACf,CAAE;YAAA5B,QAAA,gBAEFxC,OAAA,CAACb,UAAU;cAACkE,OAAO,EAAC,OAAO;cAACX,KAAK,EAAC,gBAAgB;cAAAF,QAAA,EAAC;YAEnD;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpD,OAAA,CAACb,UAAU;cAACkE,OAAO,EAAC,IAAI;cAAAb,QAAA,EACrB3C,cAAc,CAACM,QAAQ,CAACmE,SAAS,IAAI,CAAC;YAAC;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAERpD,OAAA,CAACZ,KAAK;YACJiD,EAAE,EAAE;cACFmB,CAAC,EAAE,GAAG;cACNf,OAAO,EAAE,eAAe;cACxBC,KAAK,EAAE,sBAAsB;cAC7BJ,YAAY,EAAE;YAChB,CAAE;YAAAE,QAAA,gBAEFxC,OAAA,CAACb,UAAU;cAACkE,OAAO,EAAC,OAAO;cAACX,KAAK,EAAC,SAAS;cAAAF,QAAA,EAAC;YAE5C;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpD,OAAA,CAACb,UAAU;cAACkE,OAAO,EAAC,IAAI;cAACK,UAAU,EAAC,MAAM;cAAAlB,QAAA,EACvC3C,cAAc,CAACS,eAAe;YAAC;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERpD,OAAA,CAACZ,KAAK;QAACiE,OAAO,EAAC,UAAU;QAAChB,EAAE,EAAE;UAAEmB,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAjB,QAAA,gBAC5CxC,OAAA,CAACb,UAAU;UAACkE,OAAO,EAAC,WAAW;UAACK,UAAU,EAAC,MAAM;UAACC,YAAY;UAACtB,EAAE,EAAE;YAAEM,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEG,GAAG,EAAE;UAAE,CAAE;UAAAR,QAAA,gBACnHxC,OAAA,CAACN,WAAW;YAACkE,QAAQ,EAAC,OAAO;YAAClB,KAAK,EAAC;UAAS;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,8BAElD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZrC,KAAK,iBACJf,OAAA,CAACV,KAAK;UAACiF,QAAQ,EAAC,OAAO;UAAClC,EAAE,EAAE;YAAEoB,EAAE,EAAE;UAAE,CAAE;UAAAjB,QAAA,EACnCzB;QAAK;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eAEDpD,OAAA,CAACxB,GAAG;UACFgG,SAAS,EAAC,MAAM;UAChBpD,EAAE,EAAC,cAAc;UACjBf,QAAQ,EAAEY,YAAa;UACvBoB,EAAE,EAAE;YAAEM,OAAO,EAAE,MAAM;YAAEkB,mBAAmB,EAAE;cAAEC,EAAE,EAAE,KAAK;cAAEC,EAAE,EAAE;YAAU,CAAC;YAAEf,GAAG,EAAE;UAAE,CAAE;UAAAR,QAAA,gBAEnFxC,OAAA,CAAClB,SAAS;YACR2F,KAAK,EAAC,iCAAoB;YAC1BC,IAAI,EAAC,QAAQ;YACbtC,SAAS;YACTuC,KAAK,EAAElE,aAAc;YACrBmE,QAAQ,EAAG1D,CAAC,IAAKR,gBAAgB,CAACmE,MAAM,CAAC3D,CAAC,CAAC4D,MAAM,CAACH,KAAK,CAAC,CAAE;YAC1DI,SAAS,EAAG7D,CAAC,IAAK;cAChB,IAAIA,CAAC,CAAC8D,GAAG,KAAK,OAAO,EAAE;gBACrB9D,CAAC,CAACC,cAAc,CAAC,CAAC;gBAClBF,YAAY,CAAC,CAAC;cAChB;YACF,CAAE;YACFoB,EAAE,EAAE;cACF,uBAAuB,EAAE;gBAAE4C,YAAY,EAAE;cAAO,CAAC;cACjD,sBAAsB,EAAE;gBACtBC,QAAQ,EAAE,UAAU;gBACpB,UAAU,EAAE;kBACVC,OAAO,EAAE,OAAO;kBAChBD,QAAQ,EAAE,UAAU;kBACpBE,KAAK,EAAE,MAAM;kBACbC,GAAG,EAAE,KAAK;kBACVC,SAAS,EAAE,kBAAkB;kBAC7B5C,KAAK,EAAE,qBAAqB;kBAC5B6C,aAAa,EAAE;gBACjB;cACF;YACF,CAAE;YACFxE,KAAK,EAAE,CAAC,CAACA,KAAM;YACfyE,QAAQ;UAAA;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEFpD,OAAA,CAACjB,WAAW;YAACqD,SAAS;YAACoD,QAAQ;YAAAhD,QAAA,gBAC7BxC,OAAA,CAAChB,UAAU;cAAAwD,QAAA,EAAC;YAAsB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/CpD,OAAA,CAACf,MAAM;cACL0F,KAAK,EAAEhE,aAAc;cACrB8D,KAAK,EAAC,0CAAwB;cAC9BG,QAAQ,EAAG1D,CAAC,IAAKN,gBAAgB,CAACiE,MAAM,CAAC3D,CAAC,CAAC4D,MAAM,CAACH,KAAK,CAAC,CAAE;cAAAnC,QAAA,EAEzDiD,MAAM,CAACC,OAAO,CAAC9F,gBAAgB,CAAC,CAAC+F,GAAG,CAAC,CAAC,CAAChB,KAAK,EAAEF,KAAK,CAAC,kBACnDzE,OAAA,CAACd,QAAQ;gBAAayF,KAAK,EAAEA,KAAM;gBAAAnC,QAAA,EAChCiC;cAAK,GADOE,KAAK;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEdpD,OAAA,CAACxB,GAAG;YAAC6D,EAAE,EAAE;cAAEuD,UAAU,EAAE;gBAAE9B,EAAE,EAAE,GAAG;gBAAEC,EAAE,EAAE;cAAa;YAAE,CAAE;YAAAvB,QAAA,eACrDxC,OAAA,CAAClB,SAAS;cACR2F,KAAK,EAAC,YAAS;cACfrC,SAAS;cACTyD,SAAS;cACTC,IAAI,EAAE,CAAE;cACRnB,KAAK,EAAE9D,IAAK;cACZ+D,QAAQ,EAAG1D,CAAC,IAAKJ,OAAO,CAACI,CAAC,CAAC4D,MAAM,CAACH,KAAK,CAAE;cACzCoB,WAAW,EAAC,6DAAqC;cACjDhB,SAAS,EAAG7D,CAAC,IAAK;gBAChB;gBACA,IAAIA,CAAC,CAAC8D,GAAG,KAAK,OAAO,IAAI,CAAC9D,CAAC,CAAC8E,QAAQ,EAAE;kBACpC9E,CAAC,CAAC+E,eAAe,CAAC,CAAC;gBACrB;cACF;YAAE;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEhBpD,OAAA,CAACrB,aAAa;MAAC0D,EAAE,EAAE;QAAES,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEH,cAAc,EAAE;MAAgB,CAAE;MAAAJ,QAAA,gBACnExC,OAAA,CAACvB,MAAM;QACL8E,OAAO,EAAEnD,OAAQ;QACjBsC,KAAK,EAAC,SAAS;QACfW,OAAO,EAAC,UAAU;QAClB6C,SAAS,eAAElG,OAAA,CAACR,SAAS;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAZ,QAAA,EAC1B;MAED;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTpD,OAAA,CAACvB,MAAM;QACLiG,IAAI,EAAC,QAAQ;QACbyB,IAAI,EAAC,cAAc;QACnB9C,OAAO,EAAC,WAAW;QACnBX,KAAK,EAAC,SAAS;QACfwD,SAAS,eAAElG,OAAA,CAACP,WAAW;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3BgD,IAAI,EAAC,OAAO;QACZC,QAAQ,EAAE9F,OAAO,IAAI,CAACE,aAAa,IAAIA,aAAa,IAAI,CAAE;QAAA+B,QAAA,EAEzDjC,OAAO,GAAG,eAAe,GAAG;MAAqB;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAAC5C,EAAA,CA5UIP,WAAuC;AAAAqG,EAAA,GAAvCrG,WAAuC;AA8U7C,eAAeA,WAAW;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}