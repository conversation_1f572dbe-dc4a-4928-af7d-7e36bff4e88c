{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\components\\\\contract\\\\JobDetailForm.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, TextField, Button, Typography, Paper, FormControl, InputLabel, Select, MenuItem, IconButton, Divider, Card, CardContent, useTheme, Tooltip } from '@mui/material';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport AddIcon from '@mui/icons-material/Add';\nimport WorkIcon from '@mui/icons-material/Work';\nimport LocationOnIcon from '@mui/icons-material/LocationOn';\nimport DateRangeIcon from '@mui/icons-material/DateRange';\nimport AccessTimeIcon from '@mui/icons-material/AccessTime';\nimport WorkShiftForm from './WorkShiftForm';\nimport { jobCategoryService } from '../../services/job/jobCategoryService';\nimport { ConfirmDialog, DatePickerField } from '../common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction JobDetailForm({\n  jobDetail,\n  onChange,\n  onDelete,\n  showDelete = false\n}) {\n  _s();\n  const [jobCategories, setJobCategories] = useState([]);\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\n  const theme = useTheme();\n  useEffect(() => {\n    const fetchJobCategories = async () => {\n      try {\n        const data = await jobCategoryService.getAllJobCategories();\n        setJobCategories(data);\n      } catch (error) {\n        console.error('Error fetching job categories:', error);\n      }\n    };\n    fetchJobCategories();\n  }, []);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    onChange({\n      ...jobDetail,\n      [name]: value\n    });\n  };\n  const handleSelectChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // If selecting a job category, find the name for display\n    if (name === 'jobCategoryId') {\n      const selectedCategory = jobCategories.find(cat => cat.id === value);\n      onChange({\n        ...jobDetail,\n        [name]: value,\n        jobCategoryName: selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.name\n      });\n    } else {\n      onChange({\n        ...jobDetail,\n        [name]: value\n      });\n    }\n  };\n  const handleWorkShiftChange = (index, workShift) => {\n    const updatedWorkShifts = [...(jobDetail.workShifts || [])];\n    updatedWorkShifts[index] = workShift;\n    onChange({\n      ...jobDetail,\n      workShifts: updatedWorkShifts\n    });\n  };\n  const handleAddWorkShift = () => {\n    const newWorkShift = {\n      startTime: '',\n      endTime: '',\n      numberOfWorkers: 1,\n      salary: 0,\n      workingDays: ''\n    };\n    onChange({\n      ...jobDetail,\n      workShifts: [...(jobDetail.workShifts || []), newWorkShift]\n    });\n  };\n  const handleDeleteWorkShift = index => {\n    const updatedWorkShifts = [...(jobDetail.workShifts || [])];\n    updatedWorkShifts.splice(index, 1);\n    onChange({\n      ...jobDetail,\n      workShifts: updatedWorkShifts\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    elevation: 3,\n    sx: {\n      p: 3,\n      mb: 3,\n      borderRadius: '8px',\n      border: '1px solid #e0e0e0',\n      position: 'relative',\n      overflow: 'hidden',\n      '&::before': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '6px',\n        background: theme.palette.secondary.main\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(WorkIcon, {\n          sx: {\n            mr: 1,\n            color: theme.palette.secondary.main\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          sx: {\n            fontWeight: 'bold',\n            color: theme.palette.secondary.main\n          },\n          children: \"Chi ti\\u1EBFt C\\xF4ng vi\\u1EC7c\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), showDelete && onDelete && /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"X\\xF3a c\\xF4ng vi\\u1EC7c n\\xE0y\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          color: \"error\",\n          onClick: () => setConfirmDialogOpen(true),\n          sx: {\n            border: '1px solid',\n            borderColor: theme.palette.error.main,\n            '&:hover': {\n              backgroundColor: theme.palette.error.light\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ConfirmDialog, {\n        open: confirmDialogOpen,\n        title: \"X\\xE1c nh\\u1EADn x\\xF3a\",\n        message: \"B\\u1EA1n c\\xF3 ch\\u1EAFc ch\\u1EAFn mu\\u1ED1n x\\xF3a chi ti\\u1EBFt c\\xF4ng vi\\u1EC7c n\\xE0y kh\\xF4ng? H\\xE0nh \\u0111\\u1ED9ng n\\xE0y kh\\xF4ng th\\u1EC3 ho\\xE0n t\\xE1c.\",\n        onConfirm: () => {\n          if (onDelete) onDelete();\n          setConfirmDialogOpen(false);\n        },\n        onCancel: () => setConfirmDialogOpen(false),\n        severity: \"warning\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexWrap: 'wrap',\n        gap: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: {\n            xs: '100%',\n            md: '48%'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          variant: \"outlined\",\n          sx: {\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              sx: {\n                mb: 2,\n                fontWeight: 'bold',\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(WorkIcon, {\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), \"Lo\\u1EA1i C\\xF4ng vi\\u1EC7c\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                id: \"job-category-label\",\n                children: \"Ch\\u1ECDn lo\\u1EA1i c\\xF4ng vi\\u1EC7c\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"job-category-label\",\n                id: \"jobCategoryId\",\n                name: \"jobCategoryId\",\n                value: jobDetail.jobCategoryId || '',\n                label: \"Ch\\u1ECDn lo\\u1EA1i c\\xF4ng vi\\u1EC7c\",\n                onChange: handleSelectChange,\n                required: true,\n                children: jobCategories.map(category => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: {\n            xs: '100%',\n            md: '48%'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          variant: \"outlined\",\n          sx: {\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              sx: {\n                mb: 2,\n                fontWeight: 'bold',\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(LocationOnIcon, {\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this), \"\\u0110\\u1ECBa \\u0111i\\u1EC3m l\\xE0m vi\\u1EC7c\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Nh\\u1EADp \\u0111\\u1ECBa \\u0111i\\u1EC3m l\\xE0m vi\\u1EC7c c\\u1EE5 th\\u1EC3\",\n              name: \"workLocation\",\n              value: jobDetail.workLocation || '',\n              onChange: handleInputChange,\n              onKeyDown: e => {\n                if (e.key === 'Enter') {\n                  e.preventDefault();\n                  // Don't submit on Enter in work location field\n                }\n              },\n              placeholder: \"\\u0110\\u1ECBa ch\\u1EC9 c\\u1EE5 th\\u1EC3 n\\u01A1i th\\u1EF1c hi\\u1EC7n c\\xF4ng vi\\u1EC7c (kh\\xF4ng b\\u1EAFt bu\\u1ED9c)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: {\n            xs: '100%',\n            md: '48%'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          variant: \"outlined\",\n          sx: {\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              sx: {\n                mb: 2,\n                fontWeight: 'bold',\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(DateRangeIcon, {\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this), \"Th\\u1EDDi gian th\\u1EF1c hi\\u1EC7n\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexWrap: 'wrap',\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: {\n                    xs: '100%',\n                    sm: '48%'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(DatePickerField, {\n                  label: \"Ng\\xE0y b\\u1EAFt \\u0111\\u1EA7u\",\n                  value: jobDetail.startDate || '',\n                  onChange: date => {\n                    onChange({\n                      ...jobDetail,\n                      startDate: date\n                    });\n                  },\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: {\n                    xs: '100%',\n                    sm: '48%'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(DatePickerField, {\n                  label: \"Ng\\xE0y k\\u1EBFt th\\xFAc\",\n                  value: jobDetail.endDate || '',\n                  onChange: date => {\n                    onChange({\n                      ...jobDetail,\n                      endDate: date\n                    });\n                  },\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        my: 3\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(AccessTimeIcon, {\n        sx: {\n          mr: 1,\n          color: theme.palette.info.main\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          fontWeight: 'bold',\n          color: theme.palette.info.main\n        },\n        children: \"Ca l\\xE0m vi\\u1EC7c\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 7\n    }, this), (jobDetail.workShifts || []).map((workShift, index) => /*#__PURE__*/_jsxDEV(WorkShiftForm, {\n      workShift: workShift,\n      jobDetail: jobDetail,\n      onChange: updatedWorkShift => handleWorkShiftChange(index, updatedWorkShift),\n      onDelete: () => handleDeleteWorkShift(index),\n      showDelete: true,\n      shiftIndex: index\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 9\n    }, this)), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"outlined\",\n      startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 20\n      }, this),\n      onClick: handleAddWorkShift,\n      sx: {\n        mt: 2,\n        borderColor: theme.palette.info.main,\n        color: theme.palette.info.main,\n        '&:hover': {\n          backgroundColor: theme.palette.info.light,\n          borderColor: theme.palette.info.main\n        }\n      },\n      children: \"Th\\xEAm Ca l\\xE0m vi\\u1EC7c\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this);\n}\n_s(JobDetailForm, \"niu0yeLztrMW2wXAtMw9Wwilr40=\", false, function () {\n  return [useTheme];\n});\n_c = JobDetailForm;\n;\nexport default JobDetailForm;\nvar _c;\n$RefreshReg$(_c, \"JobDetailForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "TextField", "<PERSON><PERSON>", "Typography", "Paper", "FormControl", "InputLabel", "Select", "MenuItem", "IconButton", "Divider", "Card", "<PERSON><PERSON><PERSON><PERSON>", "useTheme", "<PERSON><PERSON><PERSON>", "DeleteIcon", "AddIcon", "WorkIcon", "LocationOnIcon", "DateRangeIcon", "AccessTimeIcon", "WorkShiftForm", "jobCategoryService", "ConfirmDialog", "DatePickerField", "jsxDEV", "_jsxDEV", "JobDetailForm", "jobDetail", "onChange", "onDelete", "showDelete", "_s", "jobCategories", "setJobCategories", "confirmDialogOpen", "setConfirmDialogOpen", "theme", "fetchJobCategories", "data", "getAllJobCategories", "error", "console", "handleInputChange", "e", "name", "value", "target", "handleSelectChange", "selectedCate<PERSON><PERSON>", "find", "cat", "id", "jobCategoryName", "handleWorkShiftChange", "index", "workShift", "updatedWorkShifts", "workShifts", "handleAddWorkShift", "newWorkShift", "startTime", "endTime", "numberOfWorkers", "salary", "workingDays", "handleDeleteWorkShift", "splice", "elevation", "sx", "p", "mb", "borderRadius", "border", "position", "overflow", "content", "top", "left", "width", "height", "background", "palette", "secondary", "main", "children", "display", "justifyContent", "alignItems", "mr", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "title", "onClick", "borderColor", "backgroundColor", "light", "open", "message", "onConfirm", "onCancel", "severity", "flexWrap", "gap", "xs", "md", "fullWidth", "labelId", "jobCategoryId", "label", "required", "map", "category", "workLocation", "onKeyDown", "key", "preventDefault", "placeholder", "sm", "startDate", "date", "endDate", "my", "info", "updatedWorkShift", "shiftIndex", "startIcon", "mt", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/contract/JobDetailForm.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  Typography,\n  Paper,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  IconButton,\n  Divider,\n  Card,\n  CardContent,\n  useTheme,\n  Tooltip,\n} from '@mui/material';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport AddIcon from '@mui/icons-material/Add';\nimport WorkIcon from '@mui/icons-material/Work';\nimport LocationOnIcon from '@mui/icons-material/LocationOn';\nimport DateRangeIcon from '@mui/icons-material/DateRange';\nimport AccessTimeIcon from '@mui/icons-material/AccessTime';\nimport { JobDetail, JobCategory, WorkShift } from '../../models';\nimport WorkShiftForm from './WorkShiftForm';\nimport { jobCategoryService } from '../../services/job/jobCategoryService';\n\nimport { ConfirmDialog, DatePickerField } from '../common';\n\ninterface JobDetailFormProps {\n  jobDetail: Partial<JobDetail>;\n  onChange: (jobDetail: Partial<JobDetail>) => void;\n  onDelete?: () => void;\n  showDelete?: boolean;\n}\n\nfunction JobDetailForm({\n  jobDetail,\n  onChange,\n  onDelete,\n  showDelete = false,\n}: JobDetailFormProps) {\n  const [jobCategories, setJobCategories] = useState<JobCategory[]>([]);\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\n  const theme = useTheme();\n\n  useEffect(() => {\n    const fetchJobCategories = async () => {\n      try {\n        const data = await jobCategoryService.getAllJobCategories();\n        setJobCategories(data);\n      } catch (error) {\n        console.error('Error fetching job categories:', error);\n      }\n    };\n\n    fetchJobCategories();\n  }, []);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    onChange({\n      ...jobDetail,\n      [name]: value,\n    });\n  };\n\n  const handleSelectChange = (e: any) => {\n    const { name, value } = e.target;\n\n    // If selecting a job category, find the name for display\n    if (name === 'jobCategoryId') {\n      const selectedCategory = jobCategories.find(cat => cat.id === value);\n      onChange({\n        ...jobDetail,\n        [name]: value,\n        jobCategoryName: selectedCategory?.name\n      });\n    } else {\n      onChange({\n        ...jobDetail,\n        [name]: value,\n      });\n    }\n  };\n\n  const handleWorkShiftChange = (index: number, workShift: Partial<WorkShift>) => {\n    const updatedWorkShifts = [...(jobDetail.workShifts || [])];\n    updatedWorkShifts[index] = workShift as WorkShift;\n\n    onChange({\n      ...jobDetail,\n      workShifts: updatedWorkShifts,\n    });\n  };\n\n  const handleAddWorkShift = () => {\n    const newWorkShift: WorkShift = {\n      startTime: '',\n      endTime: '',\n      numberOfWorkers: 1,\n      salary: 0,\n      workingDays: '',\n    };\n\n    onChange({\n      ...jobDetail,\n      workShifts: [...(jobDetail.workShifts || []), newWorkShift],\n    });\n  };\n\n  const handleDeleteWorkShift = (index: number) => {\n    const updatedWorkShifts = [...(jobDetail.workShifts || [])];\n    updatedWorkShifts.splice(index, 1);\n\n    onChange({\n      ...jobDetail,\n      workShifts: updatedWorkShifts,\n    });\n  };\n\n  return (\n    <Paper\n      elevation={3}\n      sx={{\n        p: 3,\n        mb: 3,\n        borderRadius: '8px',\n        border: '1px solid #e0e0e0',\n        position: 'relative',\n        overflow: 'hidden',\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '6px',\n          background: theme.palette.secondary.main,\n        }\n      }}\n    >\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <WorkIcon sx={{ mr: 1, color: theme.palette.secondary.main }} />\n          <Typography variant=\"h5\" sx={{ fontWeight: 'bold', color: theme.palette.secondary.main }}>\n            Chi tiết Công việc\n          </Typography>\n        </Box>\n        {showDelete && onDelete && (\n          <Tooltip title=\"Xóa công việc này\">\n            <IconButton\n              color=\"error\"\n              onClick={() => setConfirmDialogOpen(true)}\n              sx={{\n                border: '1px solid',\n                borderColor: theme.palette.error.main,\n                '&:hover': {\n                  backgroundColor: theme.palette.error.light,\n                }\n              }}\n            >\n              <DeleteIcon />\n            </IconButton>\n          </Tooltip>\n        )}\n\n        <ConfirmDialog\n          open={confirmDialogOpen}\n          title=\"Xác nhận xóa\"\n          message=\"Bạn có chắc chắn muốn xóa chi tiết công việc này không? Hành động này không thể hoàn tác.\"\n          onConfirm={() => {\n            if (onDelete) onDelete();\n            setConfirmDialogOpen(false);\n          }}\n          onCancel={() => setConfirmDialogOpen(false)}\n          severity=\"warning\"\n        />\n      </Box>\n\n      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>\n        <Box sx={{ width: { xs: '100%', md: '48%' } }}>\n          <Card variant=\"outlined\" sx={{ mb: 2 }}>\n            <CardContent>\n              <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>\n                <WorkIcon sx={{ mr: 1 }} />\n                Loại Công việc\n              </Typography>\n              <FormControl fullWidth>\n                <InputLabel id=\"job-category-label\">Chọn loại công việc</InputLabel>\n                <Select\n                  labelId=\"job-category-label\"\n                  id=\"jobCategoryId\"\n                  name=\"jobCategoryId\"\n                  value={jobDetail.jobCategoryId || ''}\n                  label=\"Chọn loại công việc\"\n                  onChange={handleSelectChange}\n                  required\n                >\n                  {jobCategories.map((category) => (\n                    <MenuItem key={category.id} value={category.id}>\n                      {category.name}\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </CardContent>\n          </Card>\n        </Box>\n\n        <Box sx={{ width: { xs: '100%', md: '48%' } }}>\n          <Card variant=\"outlined\" sx={{ mb: 2 }}>\n            <CardContent>\n              <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>\n                <LocationOnIcon sx={{ mr: 1 }} />\n                Địa điểm làm việc\n              </Typography>\n              <TextField\n                fullWidth\n                label=\"Nhập địa điểm làm việc cụ thể\"\n                name=\"workLocation\"\n                value={jobDetail.workLocation || ''}\n                onChange={handleInputChange}\n                onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {\n                  if (e.key === 'Enter') {\n                    e.preventDefault();\n                    // Don't submit on Enter in work location field\n                  }\n                }}\n                placeholder=\"Địa chỉ cụ thể nơi thực hiện công việc (không bắt buộc)\"\n              />\n            </CardContent>\n          </Card>\n        </Box>\n\n        <Box sx={{ width: { xs: '100%', md: '48%' } }}>\n          <Card variant=\"outlined\" sx={{ mb: 2 }}>\n            <CardContent>\n              <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>\n                <DateRangeIcon sx={{ mr: 1 }} />\n                Thời gian thực hiện\n              </Typography>\n              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>\n                <Box sx={{ width: { xs: '100%', sm: '48%' } }}>\n                  <DatePickerField\n                    label=\"Ngày bắt đầu\"\n                    value={jobDetail.startDate || ''}\n                    onChange={(date: string) => {\n                      onChange({\n                        ...jobDetail,\n                        startDate: date\n                      });\n                    }}\n                    required\n                  />\n                </Box>\n                <Box sx={{ width: { xs: '100%', sm: '48%' } }}>\n                  <DatePickerField\n                    label=\"Ngày kết thúc\"\n                    value={jobDetail.endDate || ''}\n                    onChange={(date: string) => {\n                      onChange({\n                        ...jobDetail,\n                        endDate: date\n                      });\n                    }}\n                    required\n                  />\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Box>\n      </Box>\n\n      <Divider sx={{ my: 3 }} />\n\n      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n        <AccessTimeIcon sx={{ mr: 1, color: theme.palette.info.main }} />\n        <Typography variant=\"h6\" sx={{ fontWeight: 'bold', color: theme.palette.info.main }}>\n          Ca làm việc\n        </Typography>\n      </Box>\n\n      {(jobDetail.workShifts || []).map((workShift, index) => (\n        <WorkShiftForm\n          key={index}\n          workShift={workShift}\n          jobDetail={jobDetail}\n          onChange={(updatedWorkShift: Partial<WorkShift>) => handleWorkShiftChange(index, updatedWorkShift)}\n          onDelete={() => handleDeleteWorkShift(index)}\n          showDelete={true}\n          shiftIndex={index}\n        />\n      ))}\n\n      <Button\n        variant=\"outlined\"\n        startIcon={<AddIcon />}\n        onClick={handleAddWorkShift}\n        sx={{\n          mt: 2,\n          borderColor: theme.palette.info.main,\n          color: theme.palette.info.main,\n          '&:hover': {\n            backgroundColor: theme.palette.info.light,\n            borderColor: theme.palette.info.main,\n          }\n        }}\n      >\n        Thêm Ca làm việc\n      </Button>\n    </Paper>\n  );\n};\n\nexport default JobDetailForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,QAAQ,EACRC,OAAO,QACF,eAAe;AACtB,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,cAAc,MAAM,gCAAgC;AAE3D,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,kBAAkB,QAAQ,uCAAuC;AAE1E,SAASC,aAAa,EAAEC,eAAe,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS3D,SAASC,aAAaA,CAAC;EACrBC,SAAS;EACTC,QAAQ;EACRC,QAAQ;EACRC,UAAU,GAAG;AACK,CAAC,EAAE;EAAAC,EAAA;EACrB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAgB,EAAE,CAAC;EACrE,MAAM,CAACqC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAMuC,KAAK,GAAGxB,QAAQ,CAAC,CAAC;EAExBd,SAAS,CAAC,MAAM;IACd,MAAMuC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QACF,MAAMC,IAAI,GAAG,MAAMjB,kBAAkB,CAACkB,mBAAmB,CAAC,CAAC;QAC3DN,gBAAgB,CAACK,IAAI,CAAC;MACxB,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD;IACF,CAAC;IAEDH,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,iBAAiB,GAAIC,CAAsC,IAAK;IACpE,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChClB,QAAQ,CAAC;MACP,GAAGD,SAAS;MACZ,CAACiB,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,kBAAkB,GAAIJ,CAAM,IAAK;IACrC,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACA,IAAIF,IAAI,KAAK,eAAe,EAAE;MAC5B,MAAMI,gBAAgB,GAAGhB,aAAa,CAACiB,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,EAAE,KAAKN,KAAK,CAAC;MACpEjB,QAAQ,CAAC;QACP,GAAGD,SAAS;QACZ,CAACiB,IAAI,GAAGC,KAAK;QACbO,eAAe,EAAEJ,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEJ;MACrC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLhB,QAAQ,CAAC;QACP,GAAGD,SAAS;QACZ,CAACiB,IAAI,GAAGC;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMQ,qBAAqB,GAAGA,CAACC,KAAa,EAAEC,SAA6B,KAAK;IAC9E,MAAMC,iBAAiB,GAAG,CAAC,IAAI7B,SAAS,CAAC8B,UAAU,IAAI,EAAE,CAAC,CAAC;IAC3DD,iBAAiB,CAACF,KAAK,CAAC,GAAGC,SAAsB;IAEjD3B,QAAQ,CAAC;MACP,GAAGD,SAAS;MACZ8B,UAAU,EAAED;IACd,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,YAAuB,GAAG;MAC9BC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,EAAE;MACXC,eAAe,EAAE,CAAC;MAClBC,MAAM,EAAE,CAAC;MACTC,WAAW,EAAE;IACf,CAAC;IAEDpC,QAAQ,CAAC;MACP,GAAGD,SAAS;MACZ8B,UAAU,EAAE,CAAC,IAAI9B,SAAS,CAAC8B,UAAU,IAAI,EAAE,CAAC,EAAEE,YAAY;IAC5D,CAAC,CAAC;EACJ,CAAC;EAED,MAAMM,qBAAqB,GAAIX,KAAa,IAAK;IAC/C,MAAME,iBAAiB,GAAG,CAAC,IAAI7B,SAAS,CAAC8B,UAAU,IAAI,EAAE,CAAC,CAAC;IAC3DD,iBAAiB,CAACU,MAAM,CAACZ,KAAK,EAAE,CAAC,CAAC;IAElC1B,QAAQ,CAAC;MACP,GAAGD,SAAS;MACZ8B,UAAU,EAAED;IACd,CAAC,CAAC;EACJ,CAAC;EAED,oBACE/B,OAAA,CAACtB,KAAK;IACJgE,SAAS,EAAE,CAAE;IACbC,EAAE,EAAE;MACFC,CAAC,EAAE,CAAC;MACJC,EAAE,EAAE,CAAC;MACLC,YAAY,EAAE,KAAK;MACnBC,MAAM,EAAE,mBAAmB;MAC3BC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClB,WAAW,EAAE;QACXC,OAAO,EAAE,IAAI;QACbF,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,KAAK;QACbC,UAAU,EAAE5C,KAAK,CAAC6C,OAAO,CAACC,SAAS,CAACC;MACtC;IACF,CAAE;IAAAC,QAAA,gBAEF3D,OAAA,CAAC1B,GAAG;MAACqE,EAAE,EAAE;QAAEiB,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEjB,EAAE,EAAE;MAAE,CAAE;MAAAc,QAAA,gBACzF3D,OAAA,CAAC1B,GAAG;QAACqE,EAAE,EAAE;UAAEiB,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE;QAAS,CAAE;QAAAH,QAAA,gBACjD3D,OAAA,CAACT,QAAQ;UAACoD,EAAE,EAAE;YAAEoB,EAAE,EAAE,CAAC;YAAEC,KAAK,EAAErD,KAAK,CAAC6C,OAAO,CAACC,SAAS,CAACC;UAAK;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChEpE,OAAA,CAACvB,UAAU;UAAC4F,OAAO,EAAC,IAAI;UAAC1B,EAAE,EAAE;YAAE2B,UAAU,EAAE,MAAM;YAAEN,KAAK,EAAErD,KAAK,CAAC6C,OAAO,CAACC,SAAS,CAACC;UAAK,CAAE;UAAAC,QAAA,EAAC;QAE1F;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EACL/D,UAAU,IAAID,QAAQ,iBACrBJ,OAAA,CAACZ,OAAO;QAACmF,KAAK,EAAC,iCAAmB;QAAAZ,QAAA,eAChC3D,OAAA,CAACjB,UAAU;UACTiF,KAAK,EAAC,OAAO;UACbQ,OAAO,EAAEA,CAAA,KAAM9D,oBAAoB,CAAC,IAAI,CAAE;UAC1CiC,EAAE,EAAE;YACFI,MAAM,EAAE,WAAW;YACnB0B,WAAW,EAAE9D,KAAK,CAAC6C,OAAO,CAACzC,KAAK,CAAC2C,IAAI;YACrC,SAAS,EAAE;cACTgB,eAAe,EAAE/D,KAAK,CAAC6C,OAAO,CAACzC,KAAK,CAAC4D;YACvC;UACF,CAAE;UAAAhB,QAAA,eAEF3D,OAAA,CAACX,UAAU;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACV,eAEDpE,OAAA,CAACH,aAAa;QACZ+E,IAAI,EAAEnE,iBAAkB;QACxB8D,KAAK,EAAC,yBAAc;QACpBM,OAAO,EAAC,sKAA2F;QACnGC,SAAS,EAAEA,CAAA,KAAM;UACf,IAAI1E,QAAQ,EAAEA,QAAQ,CAAC,CAAC;UACxBM,oBAAoB,CAAC,KAAK,CAAC;QAC7B,CAAE;QACFqE,QAAQ,EAAEA,CAAA,KAAMrE,oBAAoB,CAAC,KAAK,CAAE;QAC5CsE,QAAQ,EAAC;MAAS;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENpE,OAAA,CAAC1B,GAAG;MAACqE,EAAE,EAAE;QAAEiB,OAAO,EAAE,MAAM;QAAEqB,QAAQ,EAAE,MAAM;QAAEC,GAAG,EAAE;MAAE,CAAE;MAAAvB,QAAA,gBACrD3D,OAAA,CAAC1B,GAAG;QAACqE,EAAE,EAAE;UAAEU,KAAK,EAAE;YAAE8B,EAAE,EAAE,MAAM;YAAEC,EAAE,EAAE;UAAM;QAAE,CAAE;QAAAzB,QAAA,eAC5C3D,OAAA,CAACf,IAAI;UAACoF,OAAO,EAAC,UAAU;UAAC1B,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAc,QAAA,eACrC3D,OAAA,CAACd,WAAW;YAAAyE,QAAA,gBACV3D,OAAA,CAACvB,UAAU;cAAC4F,OAAO,EAAC,WAAW;cAAC1B,EAAE,EAAE;gBAAEE,EAAE,EAAE,CAAC;gBAAEyB,UAAU,EAAE,MAAM;gBAAEV,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE;cAAS,CAAE;cAAAH,QAAA,gBACvG3D,OAAA,CAACT,QAAQ;gBAACoD,EAAE,EAAE;kBAAEoB,EAAE,EAAE;gBAAE;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,+BAE7B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpE,OAAA,CAACrB,WAAW;cAAC0G,SAAS;cAAA1B,QAAA,gBACpB3D,OAAA,CAACpB,UAAU;gBAAC8C,EAAE,EAAC,oBAAoB;gBAAAiC,QAAA,EAAC;cAAmB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpEpE,OAAA,CAACnB,MAAM;gBACLyG,OAAO,EAAC,oBAAoB;gBAC5B5D,EAAE,EAAC,eAAe;gBAClBP,IAAI,EAAC,eAAe;gBACpBC,KAAK,EAAElB,SAAS,CAACqF,aAAa,IAAI,EAAG;gBACrCC,KAAK,EAAC,uCAAqB;gBAC3BrF,QAAQ,EAAEmB,kBAAmB;gBAC7BmE,QAAQ;gBAAA9B,QAAA,EAEPpD,aAAa,CAACmF,GAAG,CAAEC,QAAQ,iBAC1B3F,OAAA,CAAClB,QAAQ;kBAAmBsC,KAAK,EAAEuE,QAAQ,CAACjE,EAAG;kBAAAiC,QAAA,EAC5CgC,QAAQ,CAACxE;gBAAI,GADDwE,QAAQ,CAACjE,EAAE;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENpE,OAAA,CAAC1B,GAAG;QAACqE,EAAE,EAAE;UAAEU,KAAK,EAAE;YAAE8B,EAAE,EAAE,MAAM;YAAEC,EAAE,EAAE;UAAM;QAAE,CAAE;QAAAzB,QAAA,eAC5C3D,OAAA,CAACf,IAAI;UAACoF,OAAO,EAAC,UAAU;UAAC1B,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAc,QAAA,eACrC3D,OAAA,CAACd,WAAW;YAAAyE,QAAA,gBACV3D,OAAA,CAACvB,UAAU;cAAC4F,OAAO,EAAC,WAAW;cAAC1B,EAAE,EAAE;gBAAEE,EAAE,EAAE,CAAC;gBAAEyB,UAAU,EAAE,MAAM;gBAAEV,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE;cAAS,CAAE;cAAAH,QAAA,gBACvG3D,OAAA,CAACR,cAAc;gBAACmD,EAAE,EAAE;kBAAEoB,EAAE,EAAE;gBAAE;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iDAEnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpE,OAAA,CAACzB,SAAS;cACR8G,SAAS;cACTG,KAAK,EAAC,0EAA+B;cACrCrE,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAElB,SAAS,CAAC0F,YAAY,IAAI,EAAG;cACpCzF,QAAQ,EAAEc,iBAAkB;cAC5B4E,SAAS,EAAG3E,CAAwC,IAAK;gBACvD,IAAIA,CAAC,CAAC4E,GAAG,KAAK,OAAO,EAAE;kBACrB5E,CAAC,CAAC6E,cAAc,CAAC,CAAC;kBAClB;gBACF;cACF,CAAE;cACFC,WAAW,EAAC;YAAyD;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENpE,OAAA,CAAC1B,GAAG;QAACqE,EAAE,EAAE;UAAEU,KAAK,EAAE;YAAE8B,EAAE,EAAE,MAAM;YAAEC,EAAE,EAAE;UAAM;QAAE,CAAE;QAAAzB,QAAA,eAC5C3D,OAAA,CAACf,IAAI;UAACoF,OAAO,EAAC,UAAU;UAAC1B,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAc,QAAA,eACrC3D,OAAA,CAACd,WAAW;YAAAyE,QAAA,gBACV3D,OAAA,CAACvB,UAAU;cAAC4F,OAAO,EAAC,WAAW;cAAC1B,EAAE,EAAE;gBAAEE,EAAE,EAAE,CAAC;gBAAEyB,UAAU,EAAE,MAAM;gBAAEV,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE;cAAS,CAAE;cAAAH,QAAA,gBACvG3D,OAAA,CAACP,aAAa;gBAACkD,EAAE,EAAE;kBAAEoB,EAAE,EAAE;gBAAE;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sCAElC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpE,OAAA,CAAC1B,GAAG;cAACqE,EAAE,EAAE;gBAAEiB,OAAO,EAAE,MAAM;gBAAEqB,QAAQ,EAAE,MAAM;gBAAEC,GAAG,EAAE;cAAE,CAAE;cAAAvB,QAAA,gBACrD3D,OAAA,CAAC1B,GAAG;gBAACqE,EAAE,EAAE;kBAAEU,KAAK,EAAE;oBAAE8B,EAAE,EAAE,MAAM;oBAAEc,EAAE,EAAE;kBAAM;gBAAE,CAAE;gBAAAtC,QAAA,eAC5C3D,OAAA,CAACF,eAAe;kBACd0F,KAAK,EAAC,gCAAc;kBACpBpE,KAAK,EAAElB,SAAS,CAACgG,SAAS,IAAI,EAAG;kBACjC/F,QAAQ,EAAGgG,IAAY,IAAK;oBAC1BhG,QAAQ,CAAC;sBACP,GAAGD,SAAS;sBACZgG,SAAS,EAAEC;oBACb,CAAC,CAAC;kBACJ,CAAE;kBACFV,QAAQ;gBAAA;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpE,OAAA,CAAC1B,GAAG;gBAACqE,EAAE,EAAE;kBAAEU,KAAK,EAAE;oBAAE8B,EAAE,EAAE,MAAM;oBAAEc,EAAE,EAAE;kBAAM;gBAAE,CAAE;gBAAAtC,QAAA,eAC5C3D,OAAA,CAACF,eAAe;kBACd0F,KAAK,EAAC,0BAAe;kBACrBpE,KAAK,EAAElB,SAAS,CAACkG,OAAO,IAAI,EAAG;kBAC/BjG,QAAQ,EAAGgG,IAAY,IAAK;oBAC1BhG,QAAQ,CAAC;sBACP,GAAGD,SAAS;sBACZkG,OAAO,EAAED;oBACX,CAAC,CAAC;kBACJ,CAAE;kBACFV,QAAQ;gBAAA;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpE,OAAA,CAAChB,OAAO;MAAC2D,EAAE,EAAE;QAAE0D,EAAE,EAAE;MAAE;IAAE;MAAApC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE1BpE,OAAA,CAAC1B,GAAG;MAACqE,EAAE,EAAE;QAAEiB,OAAO,EAAE,MAAM;QAAEE,UAAU,EAAE,QAAQ;QAAEjB,EAAE,EAAE;MAAE,CAAE;MAAAc,QAAA,gBACxD3D,OAAA,CAACN,cAAc;QAACiD,EAAE,EAAE;UAAEoB,EAAE,EAAE,CAAC;UAAEC,KAAK,EAAErD,KAAK,CAAC6C,OAAO,CAAC8C,IAAI,CAAC5C;QAAK;MAAE;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjEpE,OAAA,CAACvB,UAAU;QAAC4F,OAAO,EAAC,IAAI;QAAC1B,EAAE,EAAE;UAAE2B,UAAU,EAAE,MAAM;UAAEN,KAAK,EAAErD,KAAK,CAAC6C,OAAO,CAAC8C,IAAI,CAAC5C;QAAK,CAAE;QAAAC,QAAA,EAAC;MAErF;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAEL,CAAClE,SAAS,CAAC8B,UAAU,IAAI,EAAE,EAAE0D,GAAG,CAAC,CAAC5D,SAAS,EAAED,KAAK,kBACjD7B,OAAA,CAACL,aAAa;MAEZmC,SAAS,EAAEA,SAAU;MACrB5B,SAAS,EAAEA,SAAU;MACrBC,QAAQ,EAAGoG,gBAAoC,IAAK3E,qBAAqB,CAACC,KAAK,EAAE0E,gBAAgB,CAAE;MACnGnG,QAAQ,EAAEA,CAAA,KAAMoC,qBAAqB,CAACX,KAAK,CAAE;MAC7CxB,UAAU,EAAE,IAAK;MACjBmG,UAAU,EAAE3E;IAAM,GANbA,KAAK;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAOX,CACF,CAAC,eAEFpE,OAAA,CAACxB,MAAM;MACL6F,OAAO,EAAC,UAAU;MAClBoC,SAAS,eAAEzG,OAAA,CAACV,OAAO;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACvBI,OAAO,EAAEvC,kBAAmB;MAC5BU,EAAE,EAAE;QACF+D,EAAE,EAAE,CAAC;QACLjC,WAAW,EAAE9D,KAAK,CAAC6C,OAAO,CAAC8C,IAAI,CAAC5C,IAAI;QACpCM,KAAK,EAAErD,KAAK,CAAC6C,OAAO,CAAC8C,IAAI,CAAC5C,IAAI;QAC9B,SAAS,EAAE;UACTgB,eAAe,EAAE/D,KAAK,CAAC6C,OAAO,CAAC8C,IAAI,CAAC3B,KAAK;UACzCF,WAAW,EAAE9D,KAAK,CAAC6C,OAAO,CAAC8C,IAAI,CAAC5C;QAClC;MACF,CAAE;MAAAC,QAAA,EACH;IAED;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEZ;AAAC9D,EAAA,CAtRQL,aAAa;EAAA,QAQNd,QAAQ;AAAA;AAAAwH,EAAA,GARf1G,aAAa;AAsRrB;AAED,eAAeA,aAAa;AAAC,IAAA0G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}