{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\components\\\\payment\\\\CustomerSearchForm.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, TextField, Button, Typography, Paper, List, ListItem, ListItemText, ListItemButton, Divider, CircularProgress } from '@mui/material';\nimport SearchIcon from '@mui/icons-material/Search';\nimport PersonIcon from '@mui/icons-material/Person';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CustomerSearchForm = ({\n  onSelectCustomer,\n  onSearch,\n  loading\n}) => {\n  _s();\n  const [fullname, setFullname] = useState('');\n  const [phoneNumber, setPhoneNumber] = useState('');\n  const [searchResults, setSearchResults] = useState([]);\n  const [error, setError] = useState(null);\n  const handleSearch = async () => {\n    if (!fullname && !phoneNumber) {\n      setError('Vui lòng nhập tên hoặc số điện thoại để tìm kiếm');\n      return;\n    }\n    setError(null);\n    try {\n      const results = await onSearch(fullname, phoneNumber);\n      setSearchResults(results);\n      if (results.length === 0) {\n        setError('Không tìm thấy khách hàng nào');\n      }\n    } catch (err) {\n      console.error('Error searching customers:', err);\n      setError('Đã xảy ra lỗi khi tìm kiếm khách hàng');\n    }\n  };\n  const handleSelectCustomer = customer => {\n    onSelectCustomer(customer);\n    setSearchResults([]);\n    setFullname('');\n    setPhoneNumber('');\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    elevation: 2,\n    sx: {\n      p: 3,\n      mb: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"T\\xECm ki\\u1EBFm kh\\xE1ch h\\xE0ng\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexWrap: 'wrap',\n        gap: 2,\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(TextField, {\n        label: \"T\\xEAn kh\\xE1ch h\\xE0ng\",\n        value: fullname,\n        onChange: e => setFullname(e.target.value),\n        sx: {\n          flex: '1 1 45%',\n          minWidth: '250px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        label: \"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i\",\n        value: phoneNumber,\n        onChange: e => setPhoneNumber(e.target.value),\n        sx: {\n          flex: '1 1 45%',\n          minWidth: '250px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20,\n          color: \"inherit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 32\n        }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 81\n        }, this),\n        onClick: handleSearch,\n        disabled: loading,\n        sx: {\n          flex: '1 1 auto',\n          minWidth: '120px',\n          height: '56px'\n        },\n        children: \"T\\xECm ki\\u1EBFm\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Typography, {\n      color: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 9\n    }, this), searchResults.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        gutterBottom: true,\n        children: \"K\\u1EBFt qu\\u1EA3 t\\xECm ki\\u1EBFm\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(List, {\n        sx: {\n          width: '100%',\n          bgcolor: 'background.paper'\n        },\n        children: searchResults.map((customer, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(ListItem, {\n            disablePadding: true,\n            children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n              onClick: () => handleSelectCustomer(customer),\n              children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n                sx: {\n                  mr: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: customer.fullName,\n                secondary: /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [customer.phoneNumber, customer.companyName && ` | ${customer.companyName}`]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 17\n          }, this), index < searchResults.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 54\n          }, this)]\n        }, customer.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n};\n_s(CustomerSearchForm, \"5C2aZW5UeB83fIoO6Z7/rPnF4gg=\");\n_c = CustomerSearchForm;\nexport default CustomerSearchForm;\nvar _c;\n$RefreshReg$(_c, \"CustomerSearchForm\");", "map": {"version": 3, "names": ["React", "useState", "Box", "TextField", "<PERSON><PERSON>", "Typography", "Paper", "List", "ListItem", "ListItemText", "ListItemButton", "Divider", "CircularProgress", "SearchIcon", "PersonIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CustomerSearchForm", "onSelectCustomer", "onSearch", "loading", "_s", "fullname", "setFullname", "phoneNumber", "setPhoneNumber", "searchResults", "setSearchResults", "error", "setError", "handleSearch", "results", "length", "err", "console", "handleSelectCustomer", "customer", "elevation", "sx", "p", "mb", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "flexWrap", "gap", "label", "value", "onChange", "e", "target", "flex", "min<PERSON><PERSON><PERSON>", "startIcon", "size", "color", "onClick", "disabled", "height", "mt", "width", "bgcolor", "map", "index", "disablePadding", "mr", "primary", "fullName", "secondary", "companyName", "id", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/payment/CustomerSearchForm.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  Typography,\n  Paper,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemButton,\n  Divider,\n  CircularProgress,\n} from '@mui/material';\nimport SearchIcon from '@mui/icons-material/Search';\nimport PersonIcon from '@mui/icons-material/Person';\nimport { Customer } from '../../models';\n\ninterface CustomerSearchFormProps {\n  onSelectCustomer: (customer: Customer) => void;\n  onSearch: (fullname?: string, phoneNumber?: string) => Promise<Customer[]>;\n  loading: boolean;\n}\n\nconst CustomerSearchForm: React.FC<CustomerSearchFormProps> = ({\n  onSelectCustomer,\n  onSearch,\n  loading,\n}) => {\n  const [fullname, setFullname] = useState<string>('');\n  const [phoneNumber, setPhoneNumber] = useState<string>('');\n  const [searchResults, setSearchResults] = useState<Customer[]>([]);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleSearch = async () => {\n    if (!fullname && !phoneNumber) {\n      setError('Vui lòng nhập tên hoặc số điện thoại để tìm kiếm');\n      return;\n    }\n\n    setError(null);\n    try {\n      const results = await onSearch(fullname, phoneNumber);\n      setSearchResults(results);\n      if (results.length === 0) {\n        setError('Không tìm thấy khách hàng nào');\n      }\n    } catch (err) {\n      console.error('Error searching customers:', err);\n      setError('Đã xảy ra lỗi khi tìm kiếm khách hàng');\n    }\n  };\n\n  const handleSelectCustomer = (customer: Customer) => {\n    onSelectCustomer(customer);\n    setSearchResults([]);\n    setFullname('');\n    setPhoneNumber('');\n  };\n\n  return (\n    <Paper elevation={2} sx={{ p: 3, mb: 3 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        Tìm kiếm khách hàng\n      </Typography>\n      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>\n        <TextField\n          label=\"Tên khách hàng\"\n          value={fullname}\n          onChange={(e) => setFullname(e.target.value)}\n          sx={{ flex: '1 1 45%', minWidth: '250px' }}\n        />\n        <TextField\n          label=\"Số điện thoại\"\n          value={phoneNumber}\n          onChange={(e) => setPhoneNumber(e.target.value)}\n          sx={{ flex: '1 1 45%', minWidth: '250px' }}\n        />\n        <Button\n          variant=\"contained\"\n          startIcon={loading ? <CircularProgress size={20} color=\"inherit\" /> : <SearchIcon />}\n          onClick={handleSearch}\n          disabled={loading}\n          sx={{ flex: '1 1 auto', minWidth: '120px', height: '56px' }}\n        >\n          Tìm kiếm\n        </Button>\n      </Box>\n\n      {error && (\n        <Typography color=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Typography>\n      )}\n\n      {searchResults.length > 0 && (\n        <Box sx={{ mt: 2 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Kết quả tìm kiếm\n          </Typography>\n          <List sx={{ width: '100%', bgcolor: 'background.paper' }}>\n            {searchResults.map((customer, index) => (\n              <React.Fragment key={customer.id}>\n                <ListItem disablePadding>\n                  <ListItemButton onClick={() => handleSelectCustomer(customer)}>\n                    <PersonIcon sx={{ mr: 2 }} />\n                    <ListItemText\n                      primary={customer.fullName}\n                      secondary={\n                        <>\n                          {customer.phoneNumber}\n                          {customer.companyName && ` | ${customer.companyName}`}\n                        </>\n                      }\n                    />\n                  </ListItemButton>\n                </ListItem>\n                {index < searchResults.length - 1 && <Divider />}\n              </React.Fragment>\n            ))}\n          </List>\n        </Box>\n      )}\n    </Paper>\n  );\n};\n\nexport default CustomerSearchForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,cAAc,EACdC,OAAO,EACPC,gBAAgB,QACX,eAAe;AACtB,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,UAAU,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASpD,MAAMC,kBAAqD,GAAGA,CAAC;EAC7DC,gBAAgB;EAChBC,QAAQ;EACRC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAS,EAAE,CAAC;EACpD,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAa,EAAE,CAAC;EAClE,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAgB,IAAI,CAAC;EAEvD,MAAM+B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACR,QAAQ,IAAI,CAACE,WAAW,EAAE;MAC7BK,QAAQ,CAAC,kDAAkD,CAAC;MAC5D;IACF;IAEAA,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACF,MAAME,OAAO,GAAG,MAAMZ,QAAQ,CAACG,QAAQ,EAAEE,WAAW,CAAC;MACrDG,gBAAgB,CAACI,OAAO,CAAC;MACzB,IAAIA,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QACxBH,QAAQ,CAAC,+BAA+B,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZC,OAAO,CAACN,KAAK,CAAC,4BAA4B,EAAEK,GAAG,CAAC;MAChDJ,QAAQ,CAAC,uCAAuC,CAAC;IACnD;EACF,CAAC;EAED,MAAMM,oBAAoB,GAAIC,QAAkB,IAAK;IACnDlB,gBAAgB,CAACkB,QAAQ,CAAC;IAC1BT,gBAAgB,CAAC,EAAE,CAAC;IACpBJ,WAAW,CAAC,EAAE,CAAC;IACfE,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;EAED,oBACEX,OAAA,CAACV,KAAK;IAACiC,SAAS,EAAE,CAAE;IAACC,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACvC3B,OAAA,CAACX,UAAU;MAACuC,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbjC,OAAA,CAACd,GAAG;MAACsC,EAAE,EAAE;QAAEU,OAAO,EAAE,MAAM;QAAEC,QAAQ,EAAE,MAAM;QAAEC,GAAG,EAAE,CAAC;QAAEV,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBAC5D3B,OAAA,CAACb,SAAS;QACRkD,KAAK,EAAC,yBAAgB;QACtBC,KAAK,EAAE9B,QAAS;QAChB+B,QAAQ,EAAGC,CAAC,IAAK/B,WAAW,CAAC+B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAC7Cd,EAAE,EAAE;UAAEkB,IAAI,EAAE,SAAS;UAAEC,QAAQ,EAAE;QAAQ;MAAE;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACFjC,OAAA,CAACb,SAAS;QACRkD,KAAK,EAAC,mCAAe;QACrBC,KAAK,EAAE5B,WAAY;QACnB6B,QAAQ,EAAGC,CAAC,IAAK7B,cAAc,CAAC6B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAChDd,EAAE,EAAE;UAAEkB,IAAI,EAAE,SAAS;UAAEC,QAAQ,EAAE;QAAQ;MAAE;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACFjC,OAAA,CAACZ,MAAM;QACLwC,OAAO,EAAC,WAAW;QACnBgB,SAAS,EAAEtC,OAAO,gBAAGN,OAAA,CAACJ,gBAAgB;UAACiD,IAAI,EAAE,EAAG;UAACC,KAAK,EAAC;QAAS;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGjC,OAAA,CAACH,UAAU;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACrFc,OAAO,EAAE/B,YAAa;QACtBgC,QAAQ,EAAE1C,OAAQ;QAClBkB,EAAE,EAAE;UAAEkB,IAAI,EAAE,UAAU;UAAEC,QAAQ,EAAE,OAAO;UAAEM,MAAM,EAAE;QAAO,CAAE;QAAAtB,QAAA,EAC7D;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELnB,KAAK,iBACJd,OAAA,CAACX,UAAU;MAACyD,KAAK,EAAC,OAAO;MAACtB,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,EACrCb;IAAK;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb,EAEArB,aAAa,CAACM,MAAM,GAAG,CAAC,iBACvBlB,OAAA,CAACd,GAAG;MAACsC,EAAE,EAAE;QAAE0B,EAAE,EAAE;MAAE,CAAE;MAAAvB,QAAA,gBACjB3B,OAAA,CAACX,UAAU;QAACuC,OAAO,EAAC,WAAW;QAACC,YAAY;QAAAF,QAAA,EAAC;MAE7C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjC,OAAA,CAACT,IAAI;QAACiC,EAAE,EAAE;UAAE2B,KAAK,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAmB,CAAE;QAAAzB,QAAA,EACtDf,aAAa,CAACyC,GAAG,CAAC,CAAC/B,QAAQ,EAAEgC,KAAK,kBACjCtD,OAAA,CAAChB,KAAK,CAACiB,QAAQ;UAAA0B,QAAA,gBACb3B,OAAA,CAACR,QAAQ;YAAC+D,cAAc;YAAA5B,QAAA,eACtB3B,OAAA,CAACN,cAAc;cAACqD,OAAO,EAAEA,CAAA,KAAM1B,oBAAoB,CAACC,QAAQ,CAAE;cAAAK,QAAA,gBAC5D3B,OAAA,CAACF,UAAU;gBAAC0B,EAAE,EAAE;kBAAEgC,EAAE,EAAE;gBAAE;cAAE;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7BjC,OAAA,CAACP,YAAY;gBACXgE,OAAO,EAAEnC,QAAQ,CAACoC,QAAS;gBAC3BC,SAAS,eACP3D,OAAA,CAAAE,SAAA;kBAAAyB,QAAA,GACGL,QAAQ,CAACZ,WAAW,EACpBY,QAAQ,CAACsC,WAAW,IAAI,MAAMtC,QAAQ,CAACsC,WAAW,EAAE;gBAAA,eACrD;cACH;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,EACVqB,KAAK,GAAG1C,aAAa,CAACM,MAAM,GAAG,CAAC,iBAAIlB,OAAA,CAACL,OAAO;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA,GAf7BX,QAAQ,CAACuC,EAAE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBhB,CACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEZ,CAAC;AAAC1B,EAAA,CArGIJ,kBAAqD;AAAA2D,EAAA,GAArD3D,kBAAqD;AAuG3D,eAAeA,kBAAkB;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}