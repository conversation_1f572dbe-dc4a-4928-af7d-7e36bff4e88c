{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\components\\\\statistics\\\\CustomerInvoiceList.tsx\";\nimport React from 'react';\nimport { Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography, Box, Chip } from '@mui/material';\nimport { formatDateForDisplay } from '../../utils/dateUtils';\nimport { PaymentMethodMap } from '../../models/CustomerPayment';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CustomerInvoiceList = ({\n  invoices\n}) => {\n  // Format currency to VND\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('vi-VN', {\n      style: 'currency',\n      currency: 'VND'\n    }).format(amount);\n  };\n\n  // Format date to Vietnamese format (DD/MM/YYYY)\n  const formatDate = dateValue => {\n    if (!dateValue) {\n      return 'Không xác định';\n    }\n    try {\n      return formatDateForDisplay(dateValue) || 'Không xác định';\n    } catch (error) {\n      console.error('Error formatting date:', error, dateValue);\n      return 'Không xác định';\n    }\n  };\n\n  // Get payment method name\n  const getPaymentMethodName = methodId => {\n    return PaymentMethodMap[methodId] || 'Không xác định';\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    elevation: 2,\n    children: /*#__PURE__*/_jsxDEV(TableContainer, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"M\\xE3 h\\xF3a \\u0111\\u01A1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"M\\xE3 h\\u1EE3p \\u0111\\u1ED3ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Ng\\xE0y thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Ph\\u01B0\\u01A1ng th\\u1EE9c\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"right\",\n              children: \"S\\u1ED1 ti\\u1EC1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Ghi ch\\xFA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: invoices.length > 0 ? invoices.map(invoice => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: [\"#\", invoice.id]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: `HĐ-${invoice.customerContractId}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: formatDate(invoice.paymentDate)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: getPaymentMethodName(invoice.paymentMethod),\n                color: invoice.paymentMethod === 0 ? \"primary\" : \"default\",\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"right\",\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: formatCurrency(invoice.paymentAmount)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: invoice.note || '-'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 19\n            }, this)]\n          }, invoice.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(TableRow, {\n            children: /*#__PURE__*/_jsxDEV(TableCell, {\n              colSpan: 6,\n              align: \"center\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  py: 3\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  children: \"Kh\\xF4ng c\\xF3 h\\xF3a \\u0111\\u01A1n n\\xE0o trong kho\\u1EA3ng th\\u1EDDi gian \\u0111\\xE3 ch\\u1ECDn\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_c = CustomerInvoiceList;\nexport default CustomerInvoiceList;\nvar _c;\n$RefreshReg$(_c, \"CustomerInvoiceList\");", "map": {"version": 3, "names": ["React", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Typography", "Box", "Chip", "formatDateForDisplay", "PaymentMethodMap", "jsxDEV", "_jsxDEV", "CustomerInvoiceList", "invoices", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "formatDate", "dateValue", "error", "console", "getPaymentMethodName", "methodId", "elevation", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "align", "length", "map", "invoice", "id", "customerContractId", "paymentDate", "label", "paymentMethod", "color", "size", "sx", "fontWeight", "paymentAmount", "note", "colSpan", "py", "variant", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/statistics/CustomerInvoiceList.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Typography,\n  Box,\n  Chip\n} from '@mui/material';\nimport { formatDateForDisplay } from '../../utils/dateUtils';\nimport { CustomerPayment, PaymentMethodMap } from '../../models/CustomerPayment';\n\ninterface CustomerInvoiceListProps {\n  invoices: CustomerPayment[];\n  customerName: string;\n}\n\nconst CustomerInvoiceList: React.FC<CustomerInvoiceListProps> = ({ invoices }) => {\n  // Format currency to VND\n  const formatCurrency = (amount: number): string => {\n    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(amount);\n  };\n\n  // Format date to Vietnamese format (DD/MM/YYYY)\n  const formatDate = (dateValue: string | Date): string => {\n    if (!dateValue) {\n      return 'Không xác định';\n    }\n\n    try {\n      return formatDateForDisplay(dateValue) || 'Không xác định';\n    } catch (error) {\n      console.error('Error formatting date:', error, dateValue);\n      return 'Không xác định';\n    }\n  };\n\n  // Get payment method name\n  const getPaymentMethodName = (methodId: number): string => {\n    return PaymentMethodMap[methodId] || 'Không xác định';\n  };\n\n  return (\n    <Paper elevation={2}>\n      <TableContainer>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell>Mã hóa đơn</TableCell>\n              <TableCell>Mã hợp đồng</TableCell>\n              <TableCell>Ngày thanh toán</TableCell>\n              <TableCell>Phương thức</TableCell>\n              <TableCell align=\"right\">Số tiền</TableCell>\n              <TableCell>Ghi chú</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {invoices.length > 0 ? (\n              invoices.map((invoice) => (\n                <TableRow key={invoice.id}>\n                  <TableCell>#{invoice.id}</TableCell>\n                  <TableCell>{`HĐ-${invoice.customerContractId}`}</TableCell>\n                  <TableCell>{formatDate(invoice.paymentDate)}</TableCell>\n                  <TableCell>\n                    <Chip\n                      label={getPaymentMethodName(invoice.paymentMethod)}\n                      color={invoice.paymentMethod === 0 ? \"primary\" : \"default\"}\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell align=\"right\" sx={{ fontWeight: 'bold' }}>\n                    {formatCurrency(invoice.paymentAmount)}\n                  </TableCell>\n                  <TableCell>{invoice.note || '-'}</TableCell>\n                </TableRow>\n              ))\n            ) : (\n              <TableRow>\n                <TableCell colSpan={6} align=\"center\">\n                  <Box sx={{ py: 3 }}>\n                    <Typography variant=\"subtitle1\">\n                      Không có hóa đơn nào trong khoảng thời gian đã chọn\n                    </Typography>\n                  </Box>\n                </TableCell>\n              </TableRow>\n            )}\n          </TableBody>\n        </Table>\n      </TableContainer>\n    </Paper>\n  );\n};\n\nexport default CustomerInvoiceList;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,GAAG,EACHC,IAAI,QACC,eAAe;AACtB,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,SAA0BC,gBAAgB,QAAQ,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOjF,MAAMC,mBAAuD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAChF;EACA,MAAMC,cAAc,GAAIC,MAAc,IAAa;IACjD,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MAAEC,KAAK,EAAE,UAAU;MAAEC,QAAQ,EAAE;IAAM,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EAC9F,CAAC;;EAED;EACA,MAAMM,UAAU,GAAIC,SAAwB,IAAa;IACvD,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,gBAAgB;IACzB;IAEA,IAAI;MACF,OAAOd,oBAAoB,CAACc,SAAS,CAAC,IAAI,gBAAgB;IAC5D,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,EAAED,SAAS,CAAC;MACzD,OAAO,gBAAgB;IACzB;EACF,CAAC;;EAED;EACA,MAAMG,oBAAoB,GAAIC,QAAgB,IAAa;IACzD,OAAOjB,gBAAgB,CAACiB,QAAQ,CAAC,IAAI,gBAAgB;EACvD,CAAC;EAED,oBACEf,OAAA,CAACb,KAAK;IAAC6B,SAAS,EAAE,CAAE;IAAAC,QAAA,eAClBjB,OAAA,CAACT,cAAc;MAAA0B,QAAA,eACbjB,OAAA,CAACZ,KAAK;QAAA6B,QAAA,gBACJjB,OAAA,CAACR,SAAS;UAAAyB,QAAA,eACRjB,OAAA,CAACP,QAAQ;YAAAwB,QAAA,gBACPjB,OAAA,CAACV,SAAS;cAAA2B,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACjCrB,OAAA,CAACV,SAAS;cAAA2B,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClCrB,OAAA,CAACV,SAAS;cAAA2B,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACtCrB,OAAA,CAACV,SAAS;cAAA2B,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClCrB,OAAA,CAACV,SAAS;cAACgC,KAAK,EAAC,OAAO;cAAAL,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5CrB,OAAA,CAACV,SAAS;cAAA2B,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZrB,OAAA,CAACX,SAAS;UAAA4B,QAAA,EACPf,QAAQ,CAACqB,MAAM,GAAG,CAAC,GAClBrB,QAAQ,CAACsB,GAAG,CAAEC,OAAO,iBACnBzB,OAAA,CAACP,QAAQ;YAAAwB,QAAA,gBACPjB,OAAA,CAACV,SAAS;cAAA2B,QAAA,GAAC,GAAC,EAACQ,OAAO,CAACC,EAAE;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpCrB,OAAA,CAACV,SAAS;cAAA2B,QAAA,EAAE,MAAMQ,OAAO,CAACE,kBAAkB;YAAE;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3DrB,OAAA,CAACV,SAAS;cAAA2B,QAAA,EAAEP,UAAU,CAACe,OAAO,CAACG,WAAW;YAAC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACxDrB,OAAA,CAACV,SAAS;cAAA2B,QAAA,eACRjB,OAAA,CAACJ,IAAI;gBACHiC,KAAK,EAAEf,oBAAoB,CAACW,OAAO,CAACK,aAAa,CAAE;gBACnDC,KAAK,EAAEN,OAAO,CAACK,aAAa,KAAK,CAAC,GAAG,SAAS,GAAG,SAAU;gBAC3DE,IAAI,EAAC;cAAO;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZrB,OAAA,CAACV,SAAS;cAACgC,KAAK,EAAC,OAAO;cAACW,EAAE,EAAE;gBAAEC,UAAU,EAAE;cAAO,CAAE;cAAAjB,QAAA,EACjDd,cAAc,CAACsB,OAAO,CAACU,aAAa;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACZrB,OAAA,CAACV,SAAS;cAAA2B,QAAA,EAAEQ,OAAO,CAACW,IAAI,IAAI;YAAG;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA,GAd/BI,OAAO,CAACC,EAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAef,CACX,CAAC,gBAEFrB,OAAA,CAACP,QAAQ;YAAAwB,QAAA,eACPjB,OAAA,CAACV,SAAS;cAAC+C,OAAO,EAAE,CAAE;cAACf,KAAK,EAAC,QAAQ;cAAAL,QAAA,eACnCjB,OAAA,CAACL,GAAG;gBAACsC,EAAE,EAAE;kBAAEK,EAAE,EAAE;gBAAE,CAAE;gBAAArB,QAAA,eACjBjB,OAAA,CAACN,UAAU;kBAAC6C,OAAO,EAAC,WAAW;kBAAAtB,QAAA,EAAC;gBAEhC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QACX;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEZ,CAAC;AAACmB,EAAA,GA3EIvC,mBAAuD;AA6E7D,eAAeA,mBAAmB;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}