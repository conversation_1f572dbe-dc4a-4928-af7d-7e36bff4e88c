{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\components\\\\common\\\\SuccessAlert.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Alert, AlertTitle, Box, Collapse, IconButton } from '@mui/material';\nimport CloseIcon from '@mui/icons-material/Close';\nimport CheckCircleIcon from '@mui/icons-material/CheckCircle';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SuccessAlert = ({\n  title = 'Thành công',\n  message,\n  autoHideDuration = 5000,\n  onClose,\n  showIcon = true\n}) => {\n  _s();\n  const [open, setOpen] = useState(true);\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setOpen(false);\n      if (onClose) {\n        onClose();\n      }\n    }, autoHideDuration);\n    return () => {\n      clearTimeout(timer);\n    };\n  }, [autoHideDuration, onClose]);\n  const handleClose = () => {\n    setOpen(false);\n    if (onClose) {\n      onClose();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      my: 2\n    },\n    children: /*#__PURE__*/_jsxDEV(Collapse, {\n      in: open,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"success\",\n        icon: showIcon ? /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          fontSize: \"inherit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 28\n        }, this) : false,\n        action: /*#__PURE__*/_jsxDEV(IconButton, {\n          \"aria-label\": \"close\",\n          color: \"inherit\",\n          size: \"small\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n            fontSize: \"inherit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 13\n        }, this),\n        sx: {\n          '& .MuiAlert-message': {\n            wordBreak: 'break-word',\n            whiteSpace: 'pre-wrap'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(AlertTitle, {\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), message]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_s(SuccessAlert, \"5swwslfKOPgwwZeTkoqeSpghmqY=\");\n_c = SuccessAlert;\nexport default SuccessAlert;\nvar _c;\n$RefreshReg$(_c, \"SuccessAlert\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Box", "Collapse", "IconButton", "CloseIcon", "CheckCircleIcon", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON>", "title", "message", "autoHideDuration", "onClose", "showIcon", "_s", "open", "<PERSON><PERSON><PERSON>", "timer", "setTimeout", "clearTimeout", "handleClose", "sx", "my", "children", "in", "severity", "icon", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "action", "color", "size", "onClick", "wordBreak", "whiteSpace", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/common/SuccessAlert.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Alert, AlertTitle, Box, Collapse, IconButton } from '@mui/material';\nimport CloseIcon from '@mui/icons-material/Close';\nimport CheckCircleIcon from '@mui/icons-material/CheckCircle';\n\ninterface SuccessAlertProps {\n  title?: string;\n  message: string;\n  autoHideDuration?: number;\n  onClose?: () => void;\n  showIcon?: boolean;\n}\n\nconst SuccessAlert: React.FC<SuccessAlertProps> = ({\n  title = 'Thành công',\n  message,\n  autoHideDuration = 5000,\n  onClose,\n  showIcon = true\n}): React.ReactNode => {\n  const [open, setOpen] = useState(true);\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setOpen(false);\n      if (onClose) {\n        onClose();\n      }\n    }, autoHideDuration);\n\n    return () => {\n      clearTimeout(timer);\n    };\n  }, [autoHideDuration, onClose]);\n\n  const handleClose = () => {\n    setOpen(false);\n    if (onClose) {\n      onClose();\n    }\n  };\n\n  return (\n    <Box sx={{ my: 2 }}>\n      <Collapse in={open}>\n        <Alert\n          severity=\"success\"\n          icon={showIcon ? <CheckCircleIcon fontSize=\"inherit\" /> : false}\n          action={\n            <IconButton\n              aria-label=\"close\"\n              color=\"inherit\"\n              size=\"small\"\n              onClick={handleClose}\n            >\n              <CloseIcon fontSize=\"inherit\" />\n            </IconButton>\n          }\n          sx={{\n            '& .MuiAlert-message': {\n              wordBreak: 'break-word',\n              whiteSpace: 'pre-wrap'\n            }\n          }}\n        >\n          <AlertTitle>{title}</AlertTitle>\n          {message}\n        </Alert>\n      </Collapse>\n    </Box>\n  );\n};\n\nexport default SuccessAlert;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,UAAU,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,eAAe;AAC5E,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,eAAe,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAU9D,MAAMC,YAAyC,GAAGA,CAAC;EACjDC,KAAK,GAAG,YAAY;EACpBC,OAAO;EACPC,gBAAgB,GAAG,IAAI;EACvBC,OAAO;EACPC,QAAQ,GAAG;AACb,CAAC,KAAsB;EAAAC,EAAA;EACrB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACd,MAAMmB,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BF,OAAO,CAAC,KAAK,CAAC;MACd,IAAIJ,OAAO,EAAE;QACXA,OAAO,CAAC,CAAC;MACX;IACF,CAAC,EAAED,gBAAgB,CAAC;IAEpB,OAAO,MAAM;MACXQ,YAAY,CAACF,KAAK,CAAC;IACrB,CAAC;EACH,CAAC,EAAE,CAACN,gBAAgB,EAAEC,OAAO,CAAC,CAAC;EAE/B,MAAMQ,WAAW,GAAGA,CAAA,KAAM;IACxBJ,OAAO,CAAC,KAAK,CAAC;IACd,IAAIJ,OAAO,EAAE;MACXA,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,oBACEL,OAAA,CAACN,GAAG;IAACoB,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,eACjBhB,OAAA,CAACL,QAAQ;MAACsB,EAAE,EAAET,IAAK;MAAAQ,QAAA,eACjBhB,OAAA,CAACR,KAAK;QACJ0B,QAAQ,EAAC,SAAS;QAClBC,IAAI,EAAEb,QAAQ,gBAAGN,OAAA,CAACF,eAAe;UAACsB,QAAQ,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAAG,KAAM;QAChEC,MAAM,eACJzB,OAAA,CAACJ,UAAU;UACT,cAAW,OAAO;UAClB8B,KAAK,EAAC,SAAS;UACfC,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEf,WAAY;UAAAG,QAAA,eAErBhB,OAAA,CAACH,SAAS;YAACuB,QAAQ,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACb;QACDV,EAAE,EAAE;UACF,qBAAqB,EAAE;YACrBe,SAAS,EAAE,YAAY;YACvBC,UAAU,EAAE;UACd;QACF,CAAE;QAAAd,QAAA,gBAEFhB,OAAA,CAACP,UAAU;UAAAuB,QAAA,EAAEd;QAAK;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,EAC/BrB,OAAO;MAAA;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACjB,EAAA,CA1DIN,YAAyC;AAAA8B,EAAA,GAAzC9B,YAAyC;AA4D/C,eAAeA,YAAY;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}