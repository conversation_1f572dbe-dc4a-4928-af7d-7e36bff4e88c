{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\components\\\\payment\\\\SuccessNotification.tsx\";\nimport React from 'react';\nimport { Dialog, DialogContent, Box, Typography, Button, IconButton, Slide } from '@mui/material';\nimport CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';\nimport CloseIcon from '@mui/icons-material/Close';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Transition = /*#__PURE__*/React.forwardRef(_c = function Transition(props, ref) {\n  return /*#__PURE__*/_jsxDEV(Slide, {\n    direction: \"up\",\n    ref: ref,\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 10\n  }, this);\n});\n_c2 = Transition;\nconst SuccessNotification = ({\n  open,\n  message,\n  onClose\n}) => {\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    TransitionComponent: Transition,\n    keepMounted: true,\n    onClose: onClose,\n    \"aria-describedby\": \"success-notification-dialog\",\n    sx: {\n      '& .MuiDialog-paper': {\n        borderRadius: 2,\n        maxWidth: '400px',\n        margin: '0 auto'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 8,\n        right: 8\n      },\n      children: /*#__PURE__*/_jsxDEV(IconButton, {\n        \"aria-label\": \"close\",\n        onClick: onClose,\n        sx: {\n          color: 'text.secondary'\n        },\n        children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          textAlign: 'center',\n          py: 3,\n          px: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlineIcon, {\n          sx: {\n            fontSize: 80,\n            color: 'success.main',\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Th\\xE0nh c\\xF4ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 3\n          },\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: onClose,\n          sx: {\n            minWidth: 120\n          },\n          children: \"\\u0110\\xF3ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_c3 = SuccessNotification;\nexport default SuccessNotification;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"Transition$React.forwardRef\");\n$RefreshReg$(_c2, \"Transition\");\n$RefreshReg$(_c3, \"SuccessNotification\");", "map": {"version": 3, "names": ["React", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Box", "Typography", "<PERSON><PERSON>", "IconButton", "Slide", "CheckCircleOutlineIcon", "CloseIcon", "jsxDEV", "_jsxDEV", "Transition", "forwardRef", "_c", "props", "ref", "direction", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "SuccessNotification", "open", "message", "onClose", "TransitionComponent", "keepMounted", "sx", "borderRadius", "max<PERSON><PERSON><PERSON>", "margin", "children", "position", "top", "right", "onClick", "color", "display", "flexDirection", "alignItems", "textAlign", "py", "px", "fontSize", "mb", "variant", "gutterBottom", "min<PERSON><PERSON><PERSON>", "_c3", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/payment/SuccessNotification.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Dialog,\n  DialogContent,\n  Box,\n  Typography,\n  Button,\n  IconButton,\n  Slide,\n} from '@mui/material';\nimport { TransitionProps } from '@mui/material/transitions';\nimport CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';\nimport CloseIcon from '@mui/icons-material/Close';\n\ninterface SuccessNotificationProps {\n  open: boolean;\n  message: string;\n  onClose: () => void;\n}\n\nconst Transition = React.forwardRef(function Transition(\n  props: TransitionProps & {\n    children: React.ReactElement;\n  },\n  ref: React.Ref<unknown>,\n) {\n  return <Slide direction=\"up\" ref={ref} {...props} />;\n});\n\nconst SuccessNotification: React.FC<SuccessNotificationProps> = ({\n  open,\n  message,\n  onClose,\n}) => {\n  return (\n    <Dialog\n      open={open}\n      TransitionComponent={Transition}\n      keepMounted\n      onClose={onClose}\n      aria-describedby=\"success-notification-dialog\"\n      sx={{\n        '& .MuiDialog-paper': {\n          borderRadius: 2,\n          maxWidth: '400px',\n          margin: '0 auto',\n        },\n      }}\n    >\n      <Box sx={{ position: 'absolute', top: 8, right: 8 }}>\n        <IconButton\n          aria-label=\"close\"\n          onClick={onClose}\n          sx={{ color: 'text.secondary' }}\n        >\n          <CloseIcon />\n        </IconButton>\n      </Box>\n\n      <DialogContent>\n        <Box\n          sx={{\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            textAlign: 'center',\n            py: 3,\n            px: 1,\n          }}\n        >\n          <CheckCircleOutlineIcon\n            sx={{\n              fontSize: 80,\n              color: 'success.main',\n              mb: 2,\n            }}\n          />\n\n          <Typography variant=\"h6\" gutterBottom>\n            Thành công\n          </Typography>\n\n          <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 3 }}>\n            {message}\n          </Typography>\n\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            onClick={onClose}\n            sx={{ minWidth: 120 }}\n          >\n            Đóng\n          </Button>\n        </Box>\n      </DialogContent>\n    </Dialog>\n  );\n};\n\nexport default SuccessNotification;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,aAAa,EACbC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,UAAU,EACVC,KAAK,QACA,eAAe;AAEtB,OAAOC,sBAAsB,MAAM,wCAAwC;AAC3E,OAAOC,SAAS,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQlD,MAAMC,UAAU,gBAAGZ,KAAK,CAACa,UAAU,CAAAC,EAAA,GAAC,SAASF,UAAUA,CACrDG,KAEC,EACDC,GAAuB,EACvB;EACA,oBAAOL,OAAA,CAACJ,KAAK;IAACU,SAAS,EAAC,IAAI;IAACD,GAAG,EAAEA,GAAI;IAAA,GAAKD;EAAK;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;AACtD,CAAC,CAAC;AAACC,GAAA,GAPGV,UAAU;AAShB,MAAMW,mBAAuD,GAAGA,CAAC;EAC/DC,IAAI;EACJC,OAAO;EACPC;AACF,CAAC,KAAK;EACJ,oBACEf,OAAA,CAACV,MAAM;IACLuB,IAAI,EAAEA,IAAK;IACXG,mBAAmB,EAAEf,UAAW;IAChCgB,WAAW;IACXF,OAAO,EAAEA,OAAQ;IACjB,oBAAiB,6BAA6B;IAC9CG,EAAE,EAAE;MACF,oBAAoB,EAAE;QACpBC,YAAY,EAAE,CAAC;QACfC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE;MACV;IACF,CAAE;IAAAC,QAAA,gBAEFtB,OAAA,CAACR,GAAG;MAAC0B,EAAE,EAAE;QAAEK,QAAQ,EAAE,UAAU;QAAEC,GAAG,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAE;MAAAH,QAAA,eAClDtB,OAAA,CAACL,UAAU;QACT,cAAW,OAAO;QAClB+B,OAAO,EAAEX,OAAQ;QACjBG,EAAE,EAAE;UAAES,KAAK,EAAE;QAAiB,CAAE;QAAAL,QAAA,eAEhCtB,OAAA,CAACF,SAAS;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAENV,OAAA,CAACT,aAAa;MAAA+B,QAAA,eACZtB,OAAA,CAACR,GAAG;QACF0B,EAAE,EAAE;UACFU,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,UAAU,EAAE,QAAQ;UACpBC,SAAS,EAAE,QAAQ;UACnBC,EAAE,EAAE,CAAC;UACLC,EAAE,EAAE;QACN,CAAE;QAAAX,QAAA,gBAEFtB,OAAA,CAACH,sBAAsB;UACrBqB,EAAE,EAAE;YACFgB,QAAQ,EAAE,EAAE;YACZP,KAAK,EAAE,cAAc;YACrBQ,EAAE,EAAE;UACN;QAAE;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEFV,OAAA,CAACP,UAAU;UAAC2C,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAf,QAAA,EAAC;QAEtC;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbV,OAAA,CAACP,UAAU;UAAC2C,OAAO,EAAC,OAAO;UAACT,KAAK,EAAC,gBAAgB;UAACT,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAE,CAAE;UAAAb,QAAA,EAC9DR;QAAO;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEbV,OAAA,CAACN,MAAM;UACL0C,OAAO,EAAC,WAAW;UACnBT,KAAK,EAAC,SAAS;UACfD,OAAO,EAAEX,OAAQ;UACjBG,EAAE,EAAE;YAAEoB,QAAQ,EAAE;UAAI,CAAE;UAAAhB,QAAA,EACvB;QAED;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAAC6B,GAAA,GArEI3B,mBAAuD;AAuE7D,eAAeA,mBAAmB;AAAC,IAAAT,EAAA,EAAAQ,GAAA,EAAA4B,GAAA;AAAAC,YAAA,CAAArC,EAAA;AAAAqC,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}