{"ast": null, "code": "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport { within } from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n  var state = _ref.state,\n    name = _ref.name,\n    options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\nfunction effect(_ref2) {\n  var state = _ref2.state,\n    options = _ref2.options;\n  var _options$element = options.element,\n    arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n    if (!arrowElement) {\n      return;\n    }\n  }\n  if (!contains(state.elements.popper, arrowElement)) {\n    return;\n  }\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "map": {"version": 3, "names": ["getBasePlacement", "getLayoutRect", "contains", "getOffsetParent", "getMainAxisFromPlacement", "within", "mergePaddingObject", "expandToHashMap", "left", "right", "basePlacements", "top", "bottom", "toPaddingObject", "padding", "state", "Object", "assign", "rects", "placement", "arrow", "_ref", "_state$modifiersData$", "name", "options", "arrowElement", "elements", "popperOffsets", "modifiersData", "basePlacement", "axis", "isVertical", "indexOf", "len", "paddingObject", "arrowRect", "minProp", "maxProp", "endDiff", "reference", "popper", "startDiff", "arrowOffsetParent", "clientSize", "clientHeight", "clientWidth", "centerToReference", "min", "max", "center", "offset", "axisProp", "centerOffset", "effect", "_ref2", "_options$element", "element", "querySelector", "enabled", "phase", "fn", "requires", "requiresIfExists"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@popperjs/core/lib/modifiers/arrow.js"], "sourcesContent": ["import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport { within } from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,wBAAwB,MAAM,sCAAsC;AAC3E,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,eAAe,MAAM,6BAA6B;AACzD,SAASC,IAAI,EAAEC,KAAK,EAAEC,cAAc,EAAEC,GAAG,EAAEC,MAAM,QAAQ,aAAa,CAAC,CAAC;;AAExE,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,OAAO,EAAEC,KAAK,EAAE;EAC7DD,OAAO,GAAG,OAAOA,OAAO,KAAK,UAAU,GAAGA,OAAO,CAACE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,KAAK,CAACG,KAAK,EAAE;IAC/EC,SAAS,EAAEJ,KAAK,CAACI;EACnB,CAAC,CAAC,CAAC,GAAGL,OAAO;EACb,OAAOR,kBAAkB,CAAC,OAAOQ,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGP,eAAe,CAACO,OAAO,EAAEJ,cAAc,CAAC,CAAC;AAC7G,CAAC;AAED,SAASU,KAAKA,CAACC,IAAI,EAAE;EACnB,IAAIC,qBAAqB;EAEzB,IAAIP,KAAK,GAAGM,IAAI,CAACN,KAAK;IAClBQ,IAAI,GAAGF,IAAI,CAACE,IAAI;IAChBC,OAAO,GAAGH,IAAI,CAACG,OAAO;EAC1B,IAAIC,YAAY,GAAGV,KAAK,CAACW,QAAQ,CAACN,KAAK;EACvC,IAAIO,aAAa,GAAGZ,KAAK,CAACa,aAAa,CAACD,aAAa;EACrD,IAAIE,aAAa,GAAG7B,gBAAgB,CAACe,KAAK,CAACI,SAAS,CAAC;EACrD,IAAIW,IAAI,GAAG1B,wBAAwB,CAACyB,aAAa,CAAC;EAClD,IAAIE,UAAU,GAAG,CAACvB,IAAI,EAAEC,KAAK,CAAC,CAACuB,OAAO,CAACH,aAAa,CAAC,IAAI,CAAC;EAC1D,IAAII,GAAG,GAAGF,UAAU,GAAG,QAAQ,GAAG,OAAO;EAEzC,IAAI,CAACN,YAAY,IAAI,CAACE,aAAa,EAAE;IACnC;EACF;EAEA,IAAIO,aAAa,GAAGrB,eAAe,CAACW,OAAO,CAACV,OAAO,EAAEC,KAAK,CAAC;EAC3D,IAAIoB,SAAS,GAAGlC,aAAa,CAACwB,YAAY,CAAC;EAC3C,IAAIW,OAAO,GAAGN,IAAI,KAAK,GAAG,GAAGnB,GAAG,GAAGH,IAAI;EACvC,IAAI6B,OAAO,GAAGP,IAAI,KAAK,GAAG,GAAGlB,MAAM,GAAGH,KAAK;EAC3C,IAAI6B,OAAO,GAAGvB,KAAK,CAACG,KAAK,CAACqB,SAAS,CAACN,GAAG,CAAC,GAAGlB,KAAK,CAACG,KAAK,CAACqB,SAAS,CAACT,IAAI,CAAC,GAAGH,aAAa,CAACG,IAAI,CAAC,GAAGf,KAAK,CAACG,KAAK,CAACsB,MAAM,CAACP,GAAG,CAAC;EACtH,IAAIQ,SAAS,GAAGd,aAAa,CAACG,IAAI,CAAC,GAAGf,KAAK,CAACG,KAAK,CAACqB,SAAS,CAACT,IAAI,CAAC;EACjE,IAAIY,iBAAiB,GAAGvC,eAAe,CAACsB,YAAY,CAAC;EACrD,IAAIkB,UAAU,GAAGD,iBAAiB,GAAGZ,IAAI,KAAK,GAAG,GAAGY,iBAAiB,CAACE,YAAY,IAAI,CAAC,GAAGF,iBAAiB,CAACG,WAAW,IAAI,CAAC,GAAG,CAAC;EAChI,IAAIC,iBAAiB,GAAGR,OAAO,GAAG,CAAC,GAAGG,SAAS,GAAG,CAAC,CAAC,CAAC;EACrD;;EAEA,IAAIM,GAAG,GAAGb,aAAa,CAACE,OAAO,CAAC;EAChC,IAAIY,GAAG,GAAGL,UAAU,GAAGR,SAAS,CAACF,GAAG,CAAC,GAAGC,aAAa,CAACG,OAAO,CAAC;EAC9D,IAAIY,MAAM,GAAGN,UAAU,GAAG,CAAC,GAAGR,SAAS,CAACF,GAAG,CAAC,GAAG,CAAC,GAAGa,iBAAiB;EACpE,IAAII,MAAM,GAAG7C,MAAM,CAAC0C,GAAG,EAAEE,MAAM,EAAED,GAAG,CAAC,CAAC,CAAC;;EAEvC,IAAIG,QAAQ,GAAGrB,IAAI;EACnBf,KAAK,CAACa,aAAa,CAACL,IAAI,CAAC,IAAID,qBAAqB,GAAG,CAAC,CAAC,EAAEA,qBAAqB,CAAC6B,QAAQ,CAAC,GAAGD,MAAM,EAAE5B,qBAAqB,CAAC8B,YAAY,GAAGF,MAAM,GAAGD,MAAM,EAAE3B,qBAAqB,CAAC;AACjL;AAEA,SAAS+B,MAAMA,CAACC,KAAK,EAAE;EACrB,IAAIvC,KAAK,GAAGuC,KAAK,CAACvC,KAAK;IACnBS,OAAO,GAAG8B,KAAK,CAAC9B,OAAO;EAC3B,IAAI+B,gBAAgB,GAAG/B,OAAO,CAACgC,OAAO;IAClC/B,YAAY,GAAG8B,gBAAgB,KAAK,KAAK,CAAC,GAAG,qBAAqB,GAAGA,gBAAgB;EAEzF,IAAI9B,YAAY,IAAI,IAAI,EAAE;IACxB;EACF,CAAC,CAAC;;EAGF,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;IACpCA,YAAY,GAAGV,KAAK,CAACW,QAAQ,CAACc,MAAM,CAACiB,aAAa,CAAChC,YAAY,CAAC;IAEhE,IAAI,CAACA,YAAY,EAAE;MACjB;IACF;EACF;EAEA,IAAI,CAACvB,QAAQ,CAACa,KAAK,CAACW,QAAQ,CAACc,MAAM,EAAEf,YAAY,CAAC,EAAE;IAClD;EACF;EAEAV,KAAK,CAACW,QAAQ,CAACN,KAAK,GAAGK,YAAY;AACrC,CAAC,CAAC;;AAGF,eAAe;EACbF,IAAI,EAAE,OAAO;EACbmC,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE,MAAM;EACbC,EAAE,EAAExC,KAAK;EACTiC,MAAM,EAAEA,MAAM;EACdQ,QAAQ,EAAE,CAAC,eAAe,CAAC;EAC3BC,gBAAgB,EAAE,CAAC,iBAAiB;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}