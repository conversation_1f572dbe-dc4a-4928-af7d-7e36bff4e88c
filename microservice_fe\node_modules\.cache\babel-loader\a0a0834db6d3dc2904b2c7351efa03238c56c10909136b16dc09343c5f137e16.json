{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\components\\\\common\\\\PageHeader.tsx\";\nimport React from 'react';\nimport { Typography, Box, Divider } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction PageHeader({\n  title,\n  subtitle\n}) {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      mb: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      component: \"h1\",\n      gutterBottom: true,\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), subtitle && /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"subtitle1\",\n      color: \"text.secondary\",\n      children: subtitle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        mt: 2\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n}\n_c = PageHeader;\n;\nexport default PageHeader;\nvar _c;\n$RefreshReg$(_c, \"PageHeader\");", "map": {"version": 3, "names": ["React", "Typography", "Box", "Divider", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON>", "title", "subtitle", "sx", "mb", "children", "variant", "component", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "mt", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/common/PageHeader.tsx"], "sourcesContent": ["import React from 'react';\nimport { Typography, Box, Divider } from '@mui/material';\n\ninterface PageHeaderProps {\n  title: string;\n  subtitle?: string;\n}\n\nfunction PageHeader({ title, subtitle }: PageHeaderProps) {\n  return (\n    <Box sx={{ mb: 4 }}>\n      <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n        {title}\n      </Typography>\n      {subtitle && (\n        <Typography variant=\"subtitle1\" color=\"text.secondary\">\n          {subtitle}\n        </Typography>\n      )}\n      <Divider sx={{ mt: 2 }} />\n    </Box>\n  );\n};\n\nexport default PageHeader;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,GAAG,EAAEC,OAAO,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOzD,SAASC,UAAUA,CAAC;EAAEC,KAAK;EAAEC;AAA0B,CAAC,EAAE;EACxD,oBACEH,OAAA,CAACH,GAAG;IAACO,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACjBN,OAAA,CAACJ,UAAU;MAACW,OAAO,EAAC,IAAI;MAACC,SAAS,EAAC,IAAI;MAACC,YAAY;MAAAH,QAAA,EACjDJ;IAAK;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EACZV,QAAQ,iBACPH,OAAA,CAACJ,UAAU;MAACW,OAAO,EAAC,WAAW;MAACO,KAAK,EAAC,gBAAgB;MAAAR,QAAA,EACnDH;IAAQ;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACb,eACDb,OAAA,CAACF,OAAO;MAACM,EAAE,EAAE;QAAEW,EAAE,EAAE;MAAE;IAAE;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvB,CAAC;AAEV;AAACG,EAAA,GAdQf,UAAU;AAclB;AAED,eAAeA,UAAU;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}