{"ast": null, "code": "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "map": {"version": 3, "names": ["getWindow", "getWindowScroll", "node", "win", "scrollLeft", "pageXOffset", "scrollTop", "pageYOffset"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js"], "sourcesContent": ["import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,eAAe,SAASC,eAAeA,CAACC,IAAI,EAAE;EAC5C,IAAIC,GAAG,GAAGH,SAAS,CAACE,IAAI,CAAC;EACzB,IAAIE,UAAU,GAAGD,GAAG,CAACE,WAAW;EAChC,IAAIC,SAAS,GAAGH,GAAG,CAACI,WAAW;EAC/B,OAAO;IACLH,UAAU,EAAEA,UAAU;IACtBE,SAAS,EAAEA;EACb,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}